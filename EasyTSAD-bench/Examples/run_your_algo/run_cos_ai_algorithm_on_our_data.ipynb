{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b936432b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully imported cos_ai_service modules\n"]}], "source": ["from typing import Dict\n", "import numpy as np\n", "import sys\n", "from joblib import Parallel, delayed\n", "sys.path.append(\"../../\")\n", "sys.path.append(\"../../../cos_ai_service\")  # Add root directory to path\n", "from EasyTSAD.Controller import TSADController\n", "\n", "# Import cos_ai_service modules - use exact imports from the original code\n", "from model.data_preprocess import smooth_ewma, downsample\n", "from model.utils.waste.similarity_prod import analysis_DTW, analysis_D2, analysis_D3\n", "from model.model_static.outlier import analysis_nsigma, analysis_nsigma_sparse\n", "from model.utils.default import default\n", "from vis_data_preprocess import data_preprocess\n", "from model.data_postprocess import get_abnormal_msg\n", "print(\"Successfully imported cos_ai_service modules\")"]}, {"cell_type": "code", "execution_count": null, "id": "b3eae1f9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["(2025-08-21 17:35:23,155) [INFO]: \n", "                         \n", "███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ \n", "██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗\n", "█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║\n", "██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║\n", "███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝\n", "╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ \n", "                                                                      \n", "                         \n", "(2025-08-21 17:35:23,157) [INFO]: Dataset Directory has been loaded.\n"]}], "source": ["gctrl = TSADController()\n", "INPUT_DATA = 0\n", "\n", "\"\"\"============= [DATASET SETTINGS] =============\"\"\"\n", "# Or specify certain curves in one dataset,\n", "# e.g. AIOPS 0efb375b-b902-3661-ab23-9a0bb799f4e3 and ab216663-dcc2-3a24-b1ee-2c3e550e06c9\n", "gctrl.set_dataset(\n", "    dataset_type=\"UTS\",\n", "    dirname=\"../../../datasets\",\n", "    datasets=\"AIOPS\",\n", "    specify_curves=True,\n", "    curve_names=[\n", "        \"57051487-3a40-3828-9084-a12f7f23ee38\"\n", "    ]\n", "    #   \"0efb375b-b902-3661-ab23-9a0bb799f4e3\",\n", "    #   \"ab216663-dcc2-3a24-b1ee-2c3e550e06c9\"\n", "    #\n", ")\n", "\n", "\"\"\"============= Implement CosAIService Algorithm =============\"\"\"\n", "from EasyTSAD.Methods import BaseMethod\n", "from EasyTSAD.DataFactory import TSData\n", "\n", "class CosAIServiceAlgo(BaseMethod):\n", "    def __init__(self, params: dict) -> None:\n", "        super().__init__()\n", "        self.__anomaly_score = None\n", "        self.name = 'CosAIServiceAlgo'\n", "\n", "        # Use default parameters from cos_ai_service\n", "        self.param_config = default.copy()\n", "\n", "        # Override with any custom parameters from config\n", "        if \"param_list\" in params:\n", "            self.param_config[\"param_list\"].update(params[\"param_list\"])\n", "        if \"param_list_sparse\" in params:\n", "            self.param_config[\"param_list_sparse\"].update(params[\"param_list_sparse\"])\n", "\n", "    def _create_temporal_context(self, data_array, timestamps=None):\n", "        \"\"\"\n", "        Transform EasyTSAD single time series into cos_ai_service format exactly as expected.\n", "        Based on the data structure from cos_ai_service/vis_data_preprocess.py\n", "\n", "        Args:\n", "            data_array: 1D numpy array of time series data\n", "            timestamps: Optional timestamp array\n", "\n", "        Returns:\n", "            dict: cos_ai_service compatible data structure\n", "        \"\"\"\n", "        data_len = len(data_array)\n", "\n", "        # Split data into three roughly equal parts for today, yesterday, lastweek\n", "        # This mimics having temporal context as required by cos_ai_service\n", "        if data_len < 3*1440:\n", "            # Handle edge case for very short series\n", "            # Split data into three parts\n", "            part_size = data_len // 3\n", "            today = data_array[-part_size:].tolist()  # Most recent part\n", "            yesterday = data_array[-2*part_size:-part_size].tolist()  # Middle part\n", "            lastweek = data_array[-3*part_size:-2*part_size].tolist()  # Earliest part\n", "        elif data_len < 4*1440:\n", "            part_size = 1440\n", "            today = data_array[-part_size:].tolist()  # Most recent part\n", "            yesterday = data_array[-2*part_size:-part_size].tolist()  # Middle part\n", "            lastweek = data_array[-3*part_size:-2*part_size].tolist()  # Earliest part\n", "        elif data_len < 5*1440:\n", "            # Standard split for longer series\n", "            part_size = 1440\n", "            today = data_array[-2*part_size:].tolist()  # Most recent part\n", "            yesterday = data_array[-3*part_size:-part_size].tolist()  # Middle part\n", "            lastweek = data_array[-4*part_size:-2*part_size].tolist()  # Earliest part\n", "        else:\n", "            part_size = 1440\n", "            today = data_array[-3*part_size:].tolist()  # Most recent part\n", "            yesterday = data_array[-4*part_size:-part_size].tolist()  # Middle part\n", "            lastweek = data_array[-5*part_size:-2*part_size].tolist()  # Earliest part\n", "            \n", "\n", "        # Create timestamps if not provided\n", "        if timestamps is None:\n", "            timestamps = list(range(len(today)))\n", "\n", "        # Create the exact data structure expected by cos_ai_service data_preprocess function\n", "        # This structure matches what's expected in vis_data_preprocess.py\n", "        input_data = {\n", "            \"timestamps\": timestamps,\n", "            \"metric_data\": {\n", "                \"change_period\": True,\n", "                \"series\": [\n", "                    {\n", "                        \"metric_val\": today,\n", "                        \"val_status\": [0] * len(today)  # 0 means data is reported/valid\n", "                    },\n", "                    {\n", "                        \"metric_val\": yesterday,\n", "                        \"val_status\": [0] * len(yesterday)  # 0 means data is reported/valid\n", "                    },\n", "                    {\n", "                        \"metric_val\": lastweek,\n", "                        \"val_status\": [0] * len(lastweek)  # 0 means data is reported/valid\n", "                    }\n", "                ]\n", "            },\n", "            \"param\": self.param_config\n", "        }\n", "\n", "        return input_data\n", "\n", "    def _run_cos_ai_algorithm(self, input_data):\n", "        \"\"\"\n", "        Run the exact cos_ai_service anomaly detection algorithm.\n", "        This follows the exact flow from cos_ai_service/vis_prod_data.py\n", "\n", "        Args:\n", "            input_data: cos_ai_service compatible data structure\n", "\n", "        Returns:\n", "            dict: Updated data with anomaly detection results\n", "        \"\"\"\n", "        \n", "        # Step 1: Data preprocessing - exact call from vis_prod_data.py\n", "        metric_data = data_preprocess(input_data)\n", "\n", "        # Step 2: Run the exact algorithm flow from change_analysis() function\n", "        # Check if data is sparse (from algo_sparse vs algo_no_sparse)\n", "        is_sparse = metric_data[\"log\"][\"is_sparse\"]\n", "\n", "        if is_sparse:\n", "            # algo_sparse() implementation\n", "            param_list = metric_data[\"param\"][\"param_list_sparse\"]\n", "            metric_data = smooth_ewma(metric_data, param_smooth=param_list[\"param_smooth\"])\n", "            metric_data = analysis_nsigma_sparse(metric_data,\n", "                                                param_sigma=param_list[\"param_sigma\"],\n", "                                                time_window=int(param_list[\"time_window_nsigma\"]))\n", "            metric_data = downsample(metric_data, int(param_list[\"time_window_dtw\"]))\n", "            metric_data = analysis_D2(metric_data)\n", "        else:\n", "            # algo_no_sparse() implementation - exact sequence from vis_prod_data.py\n", "            in_change_period = metric_data[\"change_period\"]\n", "            # in_change_period = False\n", "            if not in_change_period:\n", "                param_list = metric_data[\"param\"][\"param_list_no_change\"]\n", "                metric_data = analysis_nsigma(metric_data, param_sigma=param_list[\"param_sigma\"],\n", "                                            time_window=int(param_list[\"time_window_nsigma\"]))\n", "                metric_data = downsample(metric_data, int(param_list[\"time_window_dtw\"]))\n", "                metric_data = analysis_D3(metric_data)\n", "            else:\n", "                param_list = metric_data[\"param\"][\"param_list\"]\n", "                metric_data = smooth_ewma(metric_data, param_smooth=param_list[\"param_smooth\"])\n", "                metric_data = analysis_DTW(metric_data)\n", "                metric_data = analysis_nsigma(metric_data, param_sigma=param_list[\"param_sigma\"],\n", "                                            time_window=int(param_list[\"time_window_nsigma\"]))\n", "                metric_data = downsample(metric_data, int(param_list[\"time_window_dtw\"]))\n", "                metric_data = analysis_D2(metric_data)\n", "        return metric_data\n", "\n", "\n", "    def _extract_anomaly_score(self, metric_data):\n", "        \"\"\"\n", "        Extract the exact anomaly scores from cos_ai_service results.\n", "        Use the is_change_err boolean output directly as the anomaly score.\n", "\n", "        Args:\n", "            metric_data: cos_ai_service results\n", "            original_length: Length of original EasyTSAD test data\n", "\n", "        Returns:\n", "            np.ndarray: Boolean anomaly indicators converted to float scores\n", "        \"\"\"\n", "        # Get the exact output from cos_ai_service: is_change_err boolean array\n", "        is_change_err = metric_data.get(\"is_change_err\", [])\n", "\n", "        # Convert boolean indicators to float scores (0.0 or 1.0)\n", "        # This is the exact output format from cos_ai_service\n", "        if len(is_change_err) == 0:\n", "            raise ValueError(\"No anomaly scores detected. Check cos_ai_service implementation.\")\n", "\n", "        # Use the exact boolean output from cos_ai_service\n", "        anomaly_score = is_change_err[-1]\n", "        #if anomaly_score:\n", "        #    print('alert:', anomaly_score)\n", "\n", "        return anomaly_score\n", "\n", "    def train_valid_phase(self, tsTrain: TSData):\n", "        '''\n", "        Define train and valid phase for naive mode.\n", "        For cos_ai_service algorithm, we don't need explicit training as it's based on\n", "        statistical analysis and pattern matching with historical data.\n", "        '''\n", "        # cos_ai_service is an unsupervised algorithm that doesn't require training\n", "        # It analyzes patterns in real-time using today vs yesterday vs lastweek comparisons\n", "        print(\"CosAIService algorithm: No explicit training required (unsupervised)\")\n", "        return\n", "\n", "    def train_valid_phase_all_in_one(self, tsTrains: Dict[str, TSData]):\n", "        '''\n", "        Define train and valid phase for all-in-one mode.\n", "        cos_ai_service doesn't require training across multiple time series.\n", "        '''\n", "        print(\"CosAIService algorithm: No explicit training required (unsupervised)\")\n", "        return\n", "\n", "    def test_phase_v0(self, tsData: TSData):\n", "        '''\n", "        Define test phase for each time series using the exact cos_ai_service algorithm.\n", "        This processes data iteratively with sliding windows to properly utilize\n", "        the temporal analysis capabilities of cos_ai_service.\n", "        '''\n", "        test_len = tsData.test.shape[0]\n", "        cat_data = np.concatenate([tsData.train, tsData.valid, tsData.test])\n", "\n", "        # Initialize anomaly scores array\n", "        anomaly_scores = []\n", "\n", "        for end_idx in range(-test_len, 0):\n", "\n", "            # Extract current window as \"today\"\n", "            today_data = cat_data[:end_idx]\n", "\n", "            # Create temporal context for this window\n", "            input_data = self._create_temporal_context(today_data)\n", "\n", "            global INPUT_DATA\n", "            INPUT_DATA = input_data\n", "\n", "            # Run cos_ai_service algorithm on this window\n", "            result_data = self._run_cos_ai_algorithm(input_data)\n", "\n", "            # Extract anomaly scores for this window\n", "            score = self._extract_anomaly_score(result_data)\n", "\n", "            anomaly_scores.append(score)\n", "\n", "        # Store the final anomaly scores\n", "        self.__anomaly_score = np.array(anomaly_scores)\n", "\n", "\n", "    def _process_single_window(self, end_idx, cat_data):\n", "        \"\"\"单独定义为类方法，可以被pickle序列化\"\"\"\n", "        # Extract current window as \"today\"\n", "        today_data = cat_data[:end_idx]\n", "        \n", "        # Create temporal context for this window\n", "        input_data = self._create_temporal_context(today_data)\n", "        \n", "        # Run cos_ai_service algorithm on this window\n", "        result_data = self._run_cos_ai_algorithm(input_data)\n", "        \n", "        # Extract anomaly scores for this window\n", "        score = self._extract_anomaly_score(result_data)\n", "        \n", "        return score\n", "\n", "    def test_phase(self, tsData: TSData):\n", "        '''\n", "        Define test phase for each time series using the exact cos_ai_service algorithm.\n", "        This processes data iteratively with sliding windows to properly utilize\n", "        the temporal analysis capabilities of cos_ai_service.\n", "        '''\n", "        test_len = tsData.test.shape[0]\n", "        cat_data = np.concatenate([tsData.train, tsData.valid, tsData.test])\n", "        \n", "        # 使用 joblib 并行处理所有窗口\n", "        # n_jobs=-1 表示使用所有可用的CPU核心\n", "        # backend='threading' 或 'multiprocessing' 根据需要选择\n", "        anomaly_scores = Parallel(n_jobs=16, backend='multiprocessing')(\n", "            delayed(self._process_single_window)(end_idx, cat_data) \n", "            for end_idx in range(-test_len, 0)\n", "        )\n", "        \n", "        # Store the final anomaly scores\n", "        self.__anomaly_score = np.array(anomaly_scores)\n", "\n", "\n", "    def anomaly_score(self) -> np.ndarray:\n", "        return self.__anomaly_score\n", "\n", "    def param_statistic(self, save_file):\n", "        \"\"\"Save parameter statistics following EasyTSAD conventions\"\"\"\n", "        param_info = f\"\"\"CosAIService Algorithm Parameters:\n", "\n", "Algorithm: {self.name}\n", "Type: Temporal Pattern Analysis with DTW and N-Sigma Detection\n", "Processing: EWMA Smoothing + Downsampling + Statistical Analysis\n", "\n", "Parameters Used:\n", "- param_smooth: {self.param_config['param_list']['param_smooth']}\n", "- param_dtw: {self.param_config['param_list']['param_dtw']}\n", "- param_sigma: {self.param_config['param_list']['param_sigma']}\n", "- time_window_dtw: {self.param_config['param_list']['time_window_dtw']}\n", "- time_window_nsigma: {self.param_config['param_list']['time_window_nsigma']}\n", "- time_window_focus: {self.param_config['param_list']['time_window_focus']}\n", "\n", "Sparse Parameters:\n", "- param_sigma_sparse: {self.param_config['param_list_sparse']['param_sigma']}\n", "- time_window_nsigma_sparse: {self.param_config['param_list_sparse']['time_window_nsigma']}\n", "\n", "Algorithm Flow:\n", "1. Data preprocessing and temporal context creation\n", "2. EWMA smoothing for noise reduction\n", "3. DTW analysis for pattern similarity detection\n", "4. N-sigma statistical outlier detection\n", "5. <PERSON><PERSON><PERSON> and final anomaly scoring\n", "\n", "Output: Boolean anomaly indicators (0.0/1.0) from cos_ai_service\n", "\"\"\"\n", "        with open(save_file, 'w') as f:\n", "            f.write(param_info)"]}, {"cell_type": "code", "execution_count": 3, "id": "46ef23c1", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["(2025-08-21 17:35:28,709) [INFO]: Run Experiments. Method[CosAIServiceAlgo], <PERSON><PERSON><PERSON>[naive].\n", "(2025-08-21 17:35:28,710) [INFO]: Use Customized Method Config. Path: /data/workspace/AlphaEvolve-for-Anomaly-Detector-Synthesis/EasyTSAD-bench/Examples/run_your_algo/YourAlgo.toml\n", "(2025-08-21 17:35:28,711) [INFO]:     [Load Data (Specify)] DataSets: AIOPS \n", "(2025-08-21 17:35:28,714) [INFO]:     [CosAIServiceAlgo] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["CosAIService algorithm: No explicit training required (unsupervised)\n"]}], "source": ["\"\"\"============= Run CosAIService Algorithm =============\"\"\"\n", "# Specifying methods and training schemas\n", "\n", "training_schema = \"naive\"\n", "method = \"CosAIServiceAlgo\"  # string of our cos_ai_service algorithm class\n", "\n", "# run models\n", "gctrl.run_exps(\n", "    method=method,\n", "    training_schema=training_schema,\n", "    cfg_path=\"YourAlgo.toml\" # path/to/config\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}