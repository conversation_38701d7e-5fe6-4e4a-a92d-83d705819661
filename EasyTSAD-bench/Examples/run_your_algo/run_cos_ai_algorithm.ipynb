{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b936432b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully imported cos_ai_service modules\n"]}], "source": ["from typing import Dict\n", "import numpy as np\n", "import sys\n", "sys.path.append(\"../../\")\n", "sys.path.append(\"../../../cos_ai_service\")  # Add root directory to path\n", "from EasyTSAD.Controller import TSADController\n", "\n", "# Import cos_ai_service modules - use exact imports from the original code\n", "from model.data_preprocess import smooth_ewma, downsample\n", "from model.utils.waste.similarity_prod import analysis_DTW, analysis_D2, analysis_D3\n", "from model.model_static.outlier import analysis_nsigma, analysis_nsigma_sparse\n", "from model.utils.default import default\n", "from vis_data_preprocess import data_preprocess\n", "from model.data_postprocess import get_abnormal_msg\n", "print(\"Successfully imported cos_ai_service modules\")"]}, {"cell_type": "code", "execution_count": 2, "id": "b3eae1f9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["(2025-08-20 15:46:20,390) [INFO]: \n", "                         \n", "███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ \n", "██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗\n", "█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║\n", "██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║\n", "███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝\n", "╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ \n", "                                                                      \n", "                         \n", "(2025-08-20 15:46:20,392) [INFO]: Dataset Directory has been loaded.\n"]}], "source": ["gctrl = TSADController()\n", "\n", "\"\"\"============= [DATASET SETTINGS] =============\"\"\"\n", "# Or specify certain curves in one dataset,\n", "# e.g. AIOPS 0efb375b-b902-3661-ab23-9a0bb799f4e3 and ab216663-dcc2-3a24-b1ee-2c3e550e06c9\n", "gctrl.set_dataset(\n", "    dataset_type=\"UTS\",\n", "    dirname=\"../../../datasets\",\n", "    datasets=\"AIOPS\",\n", "    #pecify_curves=True,\n", "    #urve_names=[\n", "    #   \"0efb375b-b902-3661-ab23-9a0bb799f4e3\",\n", "    #   \"ab216663-dcc2-3a24-b1ee-2c3e550e06c9\"\n", "    #\n", ")\n", "\n", "\"\"\"============= Implement CosAIService Algorithm =============\"\"\"\n", "from EasyTSAD.Methods import BaseMethod\n", "from EasyTSAD.DataFactory import TSData\n", "\n", "class CosAIServiceAlgo(BaseMethod):\n", "    def __init__(self, params: dict) -> None:\n", "        super().__init__()\n", "        self.__anomaly_score = None\n", "        self.name = 'CosAIServiceAlgo'\n", "\n", "        # Use default parameters from cos_ai_service\n", "        self.param_config = default.copy()\n", "\n", "        # Override with any custom parameters from config\n", "        if \"param_list\" in params:\n", "            self.param_config[\"param_list\"].update(params[\"param_list\"])\n", "        if \"param_list_sparse\" in params:\n", "            self.param_config[\"param_list_sparse\"].update(params[\"param_list_sparse\"])\n", "\n", "    def _create_temporal_context(self, data_array, timestamps=None):\n", "        \"\"\"\n", "        Transform EasyTSAD single time series into cos_ai_service format exactly as expected.\n", "        Based on the data structure from cos_ai_service/vis_data_preprocess.py\n", "\n", "        Args:\n", "            data_array: 1D numpy array of time series data\n", "            timestamps: Optional timestamp array\n", "\n", "        Returns:\n", "            dict: cos_ai_service compatible data structure\n", "        \"\"\"\n", "        data_len = len(data_array)\n", "\n", "        # Split data into three roughly equal parts for today, yesterday, lastweek\n", "        # This mimics having temporal context as required by cos_ai_service\n", "        if data_len < 3*1440:\n", "            # Handle edge case for very short series\n", "            # Split data into three parts\n", "            part_size = data_len // 3\n", "            today = data_array[-part_size:].tolist()  # Most recent part\n", "            yesterday = data_array[-2*part_size:-part_size].tolist()  # Middle part\n", "            lastweek = data_array[-3*part_size:-2*part_size].tolist()  # Earliest part\n", "        elif data_len < 4*1440:\n", "            part_size = 1440\n", "            today = data_array[-part_size:].tolist()  # Most recent part\n", "            yesterday = data_array[-2*part_size:-part_size].tolist()  # Middle part\n", "            lastweek = data_array[-3*part_size:-2*part_size].tolist()  # Earliest part\n", "        elif data_len < 5*1440:\n", "            # Standard split for longer series\n", "            part_size = 1440\n", "            today = data_array[-2*part_size:].tolist()  # Most recent part\n", "            yesterday = data_array[-3*part_size:-part_size].tolist()  # Middle part\n", "            lastweek = data_array[-4*part_size:-2*part_size].tolist()  # Earliest part\n", "        else:\n", "            part_size = 1440\n", "            today = data_array[-3*part_size:].tolist()  # Most recent part\n", "            yesterday = data_array[-4*part_size:-part_size].tolist()  # Middle part\n", "            lastweek = data_array[-5*part_size:-2*part_size].tolist()  # Earliest part\n", "            \n", "\n", "        # Create timestamps if not provided\n", "        if timestamps is None:\n", "            timestamps = list(range(len(today)))\n", "\n", "        # Create the exact data structure expected by cos_ai_service data_preprocess function\n", "        # This structure matches what's expected in vis_data_preprocess.py\n", "        input_data = {\n", "            \"timestamps\": timestamps,\n", "            \"metric_data\": {\n", "                \"change_period\": True,\n", "                \"series\": [\n", "                    {\n", "                        \"metric_val\": today,\n", "                        \"val_status\": [0] * len(today)  # 0 means data is reported/valid\n", "                    },\n", "                    {\n", "                        \"metric_val\": yesterday,\n", "                        \"val_status\": [0] * len(yesterday)  # 0 means data is reported/valid\n", "                    },\n", "                    {\n", "                        \"metric_val\": lastweek,\n", "                        \"val_status\": [0] * len(lastweek)  # 0 means data is reported/valid\n", "                    }\n", "                ]\n", "            },\n", "            \"param\": self.param_config\n", "        }\n", "\n", "        return input_data\n", "\n", "    def _run_cos_ai_algorithm(self, input_data):\n", "        \"\"\"\n", "        Run the exact cos_ai_service anomaly detection algorithm.\n", "        This follows the exact flow from cos_ai_service/vis_prod_data.py\n", "\n", "        Args:\n", "            input_data: cos_ai_service compatible data structure\n", "\n", "        Returns:\n", "            dict: Updated data with anomaly detection results\n", "        \"\"\"\n", "        \n", "        # Step 1: Data preprocessing - exact call from vis_prod_data.py\n", "        metric_data = data_preprocess(input_data)\n", "\n", "        # Step 2: Run the exact algorithm flow from change_analysis() function\n", "        # Check if data is sparse (from algo_sparse vs algo_no_sparse)\n", "        is_sparse = metric_data[\"log\"][\"is_sparse\"]\n", "\n", "        if is_sparse:\n", "            # algo_sparse() implementation\n", "            param_list = metric_data[\"param\"][\"param_list_sparse\"]\n", "            metric_data = smooth_ewma(metric_data, param_smooth=param_list[\"param_smooth\"])\n", "            metric_data = analysis_nsigma_sparse(metric_data,\n", "                                                param_sigma=param_list[\"param_sigma\"],\n", "                                                time_window=int(param_list[\"time_window_nsigma\"]))\n", "            metric_data = downsample(metric_data, int(param_list[\"time_window_dtw\"]))\n", "            metric_data = analysis_D2(metric_data)\n", "        else:\n", "            # algo_no_sparse() implementation - exact sequence from vis_prod_data.py\n", "            in_change_period = metric_data[\"change_period\"]\n", "            # in_change_period = False\n", "            if not in_change_period:\n", "                param_list = metric_data[\"param\"][\"param_list_no_change\"]\n", "                metric_data = analysis_nsigma(metric_data, param_sigma=param_list[\"param_sigma\"],\n", "                                            time_window=int(param_list[\"time_window_nsigma\"]))\n", "                metric_data = downsample(metric_data, int(param_list[\"time_window_dtw\"]))\n", "                metric_data = analysis_D3(metric_data)\n", "            else:\n", "                param_list = metric_data[\"param\"][\"param_list\"]\n", "                metric_data = smooth_ewma(metric_data, param_smooth=param_list[\"param_smooth\"])\n", "                metric_data = analysis_DTW(metric_data)\n", "                metric_data = analysis_nsigma(metric_data, param_sigma=param_list[\"param_sigma\"],\n", "                                            time_window=int(param_list[\"time_window_nsigma\"]))\n", "                metric_data = downsample(metric_data, int(param_list[\"time_window_dtw\"]))\n", "                metric_data = analysis_D2(metric_data)\n", "        return metric_data\n", "\n", "\n", "    def _extract_anomaly_score(self, metric_data):\n", "        \"\"\"\n", "        Extract the exact anomaly scores from cos_ai_service results.\n", "        Use the is_change_err boolean output directly as the anomaly score.\n", "\n", "        Args:\n", "            metric_data: cos_ai_service results\n", "            original_length: Length of original EasyTSAD test data\n", "\n", "        Returns:\n", "            np.ndarray: Boolean anomaly indicators converted to float scores\n", "        \"\"\"\n", "        # Get the exact output from cos_ai_service: is_change_err boolean array\n", "        is_change_err = metric_data.get(\"is_change_err\", [])\n", "\n", "        # Convert boolean indicators to float scores (0.0 or 1.0)\n", "        # This is the exact output format from cos_ai_service\n", "        if len(is_change_err) == 0:\n", "            raise ValueError(\"No anomaly scores detected. Check cos_ai_service implementation.\")\n", "\n", "        # Use the exact boolean output from cos_ai_service\n", "        anomaly_score = is_change_err[-1]\n", "        if anomaly_score:\n", "            print('alert:', anomaly_score)\n", "\n", "        return anomaly_score\n", "\n", "    def train_valid_phase(self, tsTrain: TSData):\n", "        '''\n", "        Define train and valid phase for naive mode.\n", "        For cos_ai_service algorithm, we don't need explicit training as it's based on\n", "        statistical analysis and pattern matching with historical data.\n", "        '''\n", "        # cos_ai_service is an unsupervised algorithm that doesn't require training\n", "        # It analyzes patterns in real-time using today vs yesterday vs lastweek comparisons\n", "        print(\"CosAIService algorithm: No explicit training required (unsupervised)\")\n", "        return\n", "\n", "    def train_valid_phase_all_in_one(self, tsTrains: Dict[str, TSData]):\n", "        '''\n", "        Define train and valid phase for all-in-one mode.\n", "        cos_ai_service doesn't require training across multiple time series.\n", "        '''\n", "        print(\"CosAIService algorithm: No explicit training required (unsupervised)\")\n", "        return\n", "\n", "    def test_phase(self, tsData: TSData):\n", "        '''\n", "        Define test phase for each time series using the exact cos_ai_service algorithm.\n", "        This processes data iteratively with sliding windows to properly utilize\n", "        the temporal analysis capabilities of cos_ai_service.\n", "        '''\n", "        test_len = tsData.test.shape[0]\n", "        cat_data = np.concatenate([tsData.train, tsData.valid, tsData.test])\n", "\n", "        # Initialize anomaly scores array\n", "        anomaly_scores = []\n", "\n", "        for end_idx in range(-test_len, 0):\n", "\n", "            # Extract current window as \"today\"\n", "            today_data = cat_data[:end_idx]\n", "\n", "            # Create temporal context for this window\n", "            input_data = self._create_temporal_context(today_data)\n", "\n", "            # Run cos_ai_service algorithm on this window\n", "            result_data = self._run_cos_ai_algorithm(input_data)\n", "\n", "            # Extract anomaly scores for this window\n", "            score = self._extract_anomaly_score(result_data)\n", "\n", "            anomaly_scores.append(score)\n", "\n", "        # Store the final anomaly scores\n", "        self.__anomaly_score = np.array(anomaly_scores)\n", "\n", "\n", "    def anomaly_score(self) -> np.ndarray:\n", "        return self.__anomaly_score\n", "\n", "    def param_statistic(self, save_file):\n", "        \"\"\"Save parameter statistics following EasyTSAD conventions\"\"\"\n", "        param_info = f\"\"\"CosAIService Algorithm Parameters:\n", "\n", "Algorithm: {self.name}\n", "Type: Temporal Pattern Analysis with DTW and N-Sigma Detection\n", "Processing: EWMA Smoothing + Downsampling + Statistical Analysis\n", "\n", "Parameters Used:\n", "- param_smooth: {self.param_config['param_list']['param_smooth']}\n", "- param_dtw: {self.param_config['param_list']['param_dtw']}\n", "- param_sigma: {self.param_config['param_list']['param_sigma']}\n", "- time_window_dtw: {self.param_config['param_list']['time_window_dtw']}\n", "- time_window_nsigma: {self.param_config['param_list']['time_window_nsigma']}\n", "- time_window_focus: {self.param_config['param_list']['time_window_focus']}\n", "\n", "Sparse Parameters:\n", "- param_sigma_sparse: {self.param_config['param_list_sparse']['param_sigma']}\n", "- time_window_nsigma_sparse: {self.param_config['param_list_sparse']['time_window_nsigma']}\n", "\n", "Algorithm Flow:\n", "1. Data preprocessing and temporal context creation\n", "2. EWMA smoothing for noise reduction\n", "3. DTW analysis for pattern similarity detection\n", "4. N-sigma statistical outlier detection\n", "5. <PERSON><PERSON><PERSON> and final anomaly scoring\n", "\n", "Output: Boolean anomaly indicators (0.0/1.0) from cos_ai_service\n", "\"\"\"\n", "        with open(save_file, 'w') as f:\n", "            f.write(param_info)"]}, {"cell_type": "code", "execution_count": 3, "id": "46ef23c1", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["(2025-08-20 15:46:20,403) [INFO]: Run Experiments. Method[CosAIServiceAlgo], <PERSON><PERSON><PERSON>[naive].\n", "(2025-08-20 15:46:20,405) [INFO]: Use Customized Method Config. Path: /data/workspace/AlphaEvolve-for-Anomaly-Detector-Synthesis/EasyTSAD-bench/Examples/run_your_algo/YourAlgo.toml\n", "(2025-08-20 15:46:20,406) [INFO]:     [Load Data (All)] DataSets: AIOPS \n", "(2025-08-20 15:46:20,498) [INFO]:     [CosAIServiceAlgo] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["CosAIService algorithm: No explicit training required (unsupervised)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["(2025-08-20 16:06:25,337) [INFO]:     [CosAIServiceAlgo] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c \n"]}, {"name": "stdout", "output_type": "stream", "text": ["CosAIService algorithm: No explicit training required (unsupervised)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["(2025-08-20 16:21:19,812) [INFO]:     [CosAIServiceAlgo] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["CosAIService algorithm: No explicit training required (unsupervised)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["(2025-08-20 16:41:18,932) [INFO]:     [CosAIServiceAlgo] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa \n"]}, {"name": "stdout", "output_type": "stream", "text": ["CosAIService algorithm: No explicit training required (unsupervised)\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["(2025-08-20 16:59:05,519) [INFO]:     [CosAIServiceAlgo] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "alert: 1\n", "CosAIService algorithm: No explicit training required (unsupervised)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["(2025-08-20 17:00:21,382) [INFO]:     [CosAIServiceAlgo] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["CosAIService algorithm: No explicit training required (unsupervised)\n", "alert: 1\n", "alert: 1\n", "alert: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["(2025-08-20 17:20:54,904) [INFO]:     [CosAIServiceAlgo] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["CosAIService algorithm: No explicit training required (unsupervised)\n"]}, {"ename": "ValueError", "evalue": "Buffer dtype mismatch, expected 'seq_t' but got 'long'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 8\u001b[39m\n\u001b[32m      5\u001b[39m method = \u001b[33m\"\u001b[39m\u001b[33mCosAIServiceAlgo\u001b[39m\u001b[33m\"\u001b[39m  \u001b[38;5;66;03m# string of our cos_ai_service algorithm class\u001b[39;00m\n\u001b[32m      7\u001b[39m \u001b[38;5;66;03m# run models\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m8\u001b[39m \u001b[43mgctrl\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrun_exps\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m      9\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     10\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtraining_schema\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtraining_schema\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     11\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcfg_path\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mYourAlgo.toml\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;66;43;03m# path/to/config\u001b[39;49;00m\n\u001b[32m     12\u001b[39m \u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/data/workspace/AlphaEvolve-for-Anomaly-Detector-Synthesis/EasyTSAD-bench/Examples/run_your_algo/../../EasyTSAD/Controller/TSADController.py:186\u001b[39m, in \u001b[36mTSADController.run_exps\u001b[39m\u001b[34m(self, method, training_schema, cfg_path, diff_order, preprocess, hparams)\u001b[39m\n\u001b[32m    183\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mUnknown \u001b[39m\u001b[38;5;130;01m\\\"\u001b[39;00m\u001b[33mtraining_schema\u001b[39m\u001b[38;5;130;01m\\\"\u001b[39;00m\u001b[33m, must be one of naive, all_in_one, zero_shot\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m    185\u001b[39m tsDatas = run_instance.load_data()\n\u001b[32m--> \u001b[39m\u001b[32m186\u001b[39m \u001b[43mrun_instance\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdo_exp\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtsDatas\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtsDatas\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mhparams\u001b[49m\u001b[43m=\u001b[49m\u001b[43mhparams\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/data/workspace/AlphaEvolve-for-Anomaly-Detector-Synthesis/EasyTSAD-bench/Examples/run_your_algo/../../EasyTSAD/TrainingSchema/Naive.py:60\u001b[39m, in \u001b[36mNaive.do_exp\u001b[39m\u001b[34m(self, tsD<PERSON><PERSON>, hparams)\u001b[39m\n\u001b[32m     57\u001b[39m \u001b[38;5;28mself\u001b[39m.train_valid_timer.toc()\n\u001b[32m     59\u001b[39m \u001b[38;5;28mself\u001b[39m.test_timer.tic()\n\u001b[32m---> \u001b[39m\u001b[32m60\u001b[39m \u001b[43mmethod\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtest_phase\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcurve\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     61\u001b[39m \u001b[38;5;28mself\u001b[39m.test_timer.toc()\n\u001b[32m     63\u001b[39m score = method.anomaly_score()\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 223\u001b[39m, in \u001b[36mCosAIServiceAlgo.test_phase\u001b[39m\u001b[34m(self, tsData)\u001b[39m\n\u001b[32m    220\u001b[39m input_data = \u001b[38;5;28mself\u001b[39m._create_temporal_context(today_data)\n\u001b[32m    222\u001b[39m \u001b[38;5;66;03m# Run cos_ai_service algorithm on this window\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m223\u001b[39m result_data = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_run_cos_ai_algorithm\u001b[49m\u001b[43m(\u001b[49m\u001b[43minput_data\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    225\u001b[39m \u001b[38;5;66;03m# Extract anomaly scores for this window\u001b[39;00m\n\u001b[32m    226\u001b[39m score = \u001b[38;5;28mself\u001b[39m._extract_anomaly_score(result_data)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 152\u001b[39m, in \u001b[36mCosAIServiceAlgo._run_cos_ai_algorithm\u001b[39m\u001b[34m(self, input_data)\u001b[39m\n\u001b[32m    149\u001b[39m         metric_data = analysis_nsigma(metric_data, param_sigma=param_list[\u001b[33m\"\u001b[39m\u001b[33mparam_sigma\u001b[39m\u001b[33m\"\u001b[39m],\n\u001b[32m    150\u001b[39m                                     time_window=\u001b[38;5;28mint\u001b[39m(param_list[\u001b[33m\"\u001b[39m\u001b[33mtime_window_nsigma\u001b[39m\u001b[33m\"\u001b[39m]))\n\u001b[32m    151\u001b[39m         metric_data = downsample(metric_data, \u001b[38;5;28mint\u001b[39m(param_list[\u001b[33m\"\u001b[39m\u001b[33mtime_window_dtw\u001b[39m\u001b[33m\"\u001b[39m]))\n\u001b[32m--> \u001b[39m\u001b[32m152\u001b[39m         metric_data = \u001b[43manalysis_D2\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmetric_data\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    153\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m metric_data\n", "\u001b[36mFile \u001b[39m\u001b[32m/data/workspace/AlphaEvolve-for-Anomaly-Detector-Synthesis/EasyTSAD-bench/Examples/run_your_algo/../../../cos_ai_service/model/utils/waste/similarity_prod.py:42\u001b[39m, in \u001b[36manalysis_D2\u001b[39m\u001b[34m(data)\u001b[39m\n\u001b[32m     41\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34manalysis_D2\u001b[39m(data):\n\u001b[32m---> \u001b[39m\u001b[32m42\u001b[39m     dtw_yes, dtw_week = \u001b[43mdtw\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdistance_fast\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnp\u001b[49m\u001b[43m.\u001b[49m\u001b[43marray\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mdownsample\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmetric_today\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnp\u001b[49m\u001b[43m.\u001b[49m\u001b[43marray\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mdownsample\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmetric_yesterday\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m \\\n\u001b[32m     43\u001b[39m         , dtw.distance_fast(np.array(data[\u001b[33m'\u001b[39m\u001b[33mdownsample\u001b[39m\u001b[33m'\u001b[39m][\u001b[33m\"\u001b[39m\u001b[33mmetric_today\u001b[39m\u001b[33m\"\u001b[39m]), np.array(data[\u001b[33m'\u001b[39m\u001b[33mdownsample\u001b[39m\u001b[33m'\u001b[39m][\u001b[33m\"\u001b[39m\u001b[33mmetric_lastweek\u001b[39m\u001b[33m\"\u001b[39m]))\n\u001b[32m     44\u001b[39m     dtw_all = \u001b[38;5;28mmin\u001b[39m(dtw_yes, dtw_week)\n\u001b[32m     45\u001b[39m     param_list = data[\u001b[33m\"\u001b[39m\u001b[33mparam\u001b[39m\u001b[33m\"\u001b[39m][\u001b[33m\"\u001b[39m\u001b[33mparam_list\u001b[39m\u001b[33m\"\u001b[39m]\n", "\u001b[36mFile \u001b[39m\u001b[32m/data/workspace/AlphaEvolve-for-Anomaly-Detector-Synthesis/.venv/lib64/python3.12/site-packages/dtaidistance/dtw.py:392\u001b[39m, in \u001b[36mdistance_fast\u001b[39m\u001b[34m(s1, s2, only_ub, **kwargs)\u001b[39m\n\u001b[32m    390\u001b[39m \u001b[38;5;66;03m# Move data to C library\u001b[39;00m\n\u001b[32m    391\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m s.use_ndim \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mFalse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m392\u001b[39m     d = \u001b[43mdtw_cc\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdistance\u001b[49m\u001b[43m(\u001b[49m\u001b[43ms1\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43ms2\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43monly_ub\u001b[49m\u001b[43m=\u001b[49m\u001b[43monly_ub\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43ms\u001b[49m\u001b[43m.\u001b[49m\u001b[43mc_kwargs\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    393\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    394\u001b[39m     d = dtw_cc.distance_ndim(s1, s2, only_ub=only_ub, **s.c_kwargs())\n", "\u001b[36mFile \u001b[39m\u001b[32msrc/dtaidistance/dtw_cc.pyx:319\u001b[39m, in \u001b[36mdtaidistance.dtw_cc.distance\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[31mV<PERSON><PERSON><PERSON><PERSON>r\u001b[39m: <PERSON><PERSON><PERSON> dtype mismatch, expected 'seq_t' but got 'long'"]}], "source": ["\"\"\"============= Run CosAIService Algorithm =============\"\"\"\n", "# Specifying methods and training schemas\n", "\n", "training_schema = \"naive\"\n", "method = \"CosAIServiceAlgo\"  # string of our cos_ai_service algorithm class\n", "\n", "# run models\n", "gctrl.run_exps(\n", "    method=method,\n", "    training_schema=training_schema,\n", "    cfg_path=\"YourAlgo.toml\" # path/to/config\n", ")"]}], "metadata": {"kernelspec": {"display_name": "AlphaEvolve-for-Anomaly-Detector-Synthesis", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}