{"121": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 6.8125}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 6.8125}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 6.8125}}, "135": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 15.875}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 15.875}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 15.875}}, "109": {"best f1 under pa": {"f1": 0.9956331877729252, "precision": 0.991304347826087, "recall": 1.0, "threshold": 735.0}, "event-based f1 under pa with mode log": {"f1": 0.9696969696969692, "precision": 0.9411764705882353, "recall": 1.0, "threshold": 735.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9411764705882346, "precision": 0.8888888888888887, "recall": 0.9999999999999998, "threshold": 735.0}}, "34": {"best f1 under pa": {"f1": 0.9953271028037378, "precision": 0.9906976744186047, "recall": 1.0, "threshold": 11.760000000000002}, "event-based f1 under pa with mode log": {"f1": 0.9090909090909084, "precision": 0.8333333333333333, "recall": 0.9999999999999998, "threshold": 11.760000000000002}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999992, "precision": 0.9999999999999996, "recall": 0.6666666666666665, "threshold": 29.840000000000003}}, "35": {"best f1 under pa": {"f1": 0.9877675840978588, "precision": 0.9758308157099698, "recall": 1.0, "threshold": 6.660000000000004}, "event-based f1 under pa with mode log": {"f1": 0.9230769230769225, "precision": 0.96, "recall": 0.8888888888888888, "threshold": 9.619999999999997}, "event-based f1 under pa with mode squeeze": {"f1": 0.8999999999999992, "precision": 0.8999999999999998, "recall": 0.8999999999999998, "threshold": 9.619999999999997}}, "21": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 6067.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 6067.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 6067.0}}, "108": {"best f1 under pa": {"f1": 0.9909909909909903, "precision": 0.9821428571428571, "recall": 1.0, "threshold": 838.0}, "event-based f1 under pa with mode log": {"f1": 0.9411764705882346, "precision": 0.8888888888888887, "recall": 0.9999999999999998, "threshold": 838.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.8888888888888881, "precision": 0.7999999999999998, "recall": 0.9999999999999998, "threshold": 838.0}}, "134": {"best f1 under pa": {"f1": 0.9780564263322878, "precision": 0.9570552147239264, "recall": 1.0, "threshold": 8.25}, "event-based f1 under pa with mode log": {"f1": 0.8181818181818175, "precision": 0.9999999999999998, "recall": 0.6923076923076922, "threshold": 17.5625}, "event-based f1 under pa with mode squeeze": {"f1": 0.7499999999999992, "precision": 0.9999999999999997, "recall": 0.5999999999999999, "threshold": 17.5625}}, "120": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 4.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 4.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 4.0}}, "136": {"best f1 under pa": {"f1": 0.913385826771653, "precision": 0.9206349206349206, "recall": 0.90625, "threshold": 7.875}, "event-based f1 under pa with mode log": {"f1": 0.7368421052631572, "precision": 0.8749999999999998, "recall": 0.6363636363636362, "threshold": 9.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666661, "precision": 0.7499999999999999, "recall": 0.5999999999999999, "threshold": 9.0}}, "122": {"best f1 under pa": {"f1": 0.9872611464968147, "precision": 1.0, "recall": 0.9748427672955975, "threshold": 4.4375}, "event-based f1 under pa with mode log": {"f1": 0.9230769230769225, "precision": 0.9999999999999999, "recall": 0.857142857142857, "threshold": 4.4375}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999992, "precision": 0.9999999999999996, "recall": 0.6666666666666665, "threshold": 4.4375}}, "37": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 10.049999999999997}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 10.049999999999997}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 10.049999999999997}}, "23": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 6794.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 6794.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 6794.0}}, "22": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 7243.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 7243.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 7243.0}}, "36": {"best f1 under pa": {"f1": 0.9714285714285709, "precision": 0.9444444444444444, "recall": 1.0, "threshold": 8.440000000000005}, "event-based f1 under pa with mode log": {"f1": 0.8947368421052626, "precision": 0.9444444444444444, "recall": 0.85, "threshold": 11.829999999999998}, "event-based f1 under pa with mode squeeze": {"f1": 0.8749999999999993, "precision": 0.8749999999999998, "recall": 0.8749999999999998, "threshold": 11.829999999999998}}, "123": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 5.6875}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 5.6875}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 5.6875}}, "137": {"best f1 under pa": {"f1": 0.9824561403508766, "precision": 0.9655172413793104, "recall": 1.0, "threshold": 11.733333587700002}, "event-based f1 under pa with mode log": {"f1": 0.90566037735849, "precision": 0.8888888888888888, "recall": 0.9230769230769231, "threshold": 13.4375}, "event-based f1 under pa with mode squeeze": {"f1": 0.8461538461538455, "precision": 0.7857142857142856, "recall": 0.9166666666666665, "threshold": 13.4375}}, "133": {"best f1 under pa": {"f1": 0.9836065573770486, "precision": 0.967741935483871, "recall": 1.0, "threshold": 9.0}, "event-based f1 under pa with mode log": {"f1": 0.8787878787878782, "precision": 0.7837837837837838, "recall": 1.0, "threshold": 9.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.8333333333333327, "precision": 0.909090909090909, "recall": 0.7692307692307692, "threshold": 13.00000047684}}, "32": {"best f1 under pa": {"f1": 0.994366197183098, "precision": 0.988795518207283, "recall": 1.0, "threshold": 1.3333334922800004}, "event-based f1 under pa with mode log": {"f1": 0.9574468085106378, "precision": 0.9183673469387755, "recall": 1.0, "threshold": 1.3333334922800004}, "event-based f1 under pa with mode squeeze": {"f1": 0.9268292682926823, "precision": 0.95, "recall": 0.9047619047619048, "threshold": 1.8333330154399992}}, "26": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 6506.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 6506.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 6506.0}}, "27": {"best f1 under pa": {"f1": 0.9670886075949362, "precision": 0.9362745098039216, "recall": 1.0, "threshold": 1.5}, "event-based f1 under pa with mode log": {"f1": 0.8888888888888883, "precision": 0.8421052631578947, "recall": 0.9411764705882353, "threshold": 2.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.8333333333333327, "precision": 0.7894736842105263, "recall": 0.8823529411764706, "threshold": 2.7500002384200006}}, "33": {"best f1 under pa": {"f1": 0.901869158878504, "precision": 0.8212765957446808, "recall": 1.0, "threshold": 4.549999999999997}, "event-based f1 under pa with mode log": {"f1": 0.9032258064516123, "precision": 0.9999999999999999, "recall": 0.8235294117647058, "threshold": 20.65}, "event-based f1 under pa with mode squeeze": {"f1": 0.9090909090909084, "precision": 0.9999999999999998, "recall": 0.8333333333333333, "threshold": 20.65}}, "132": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 15.625}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 15.625}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 15.625}}, "118": {"best f1 under pa": {"f1": 0.9714285714285709, "precision": 0.9444444444444444, "recall": 1.0, "threshold": 1443.0}, "event-based f1 under pa with mode log": {"f1": 0.7499999999999992, "precision": 0.857142857142857, "recall": 0.6666666666666665, "threshold": 2082.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.7499999999999994, "precision": 0.7499999999999999, "recall": 0.7499999999999999, "threshold": 2082.0}}, "124": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4.5}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 4.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 4.5}}, "25": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4641.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 4641.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 4641.0}}, "31": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.4999997615799994}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.4999997615799994}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.4999997615799994}}, "19": {"best f1 under pa": {"f1": 0.9677419354838704, "precision": 0.9375, "recall": 1.0, "threshold": 902.0}, "event-based f1 under pa with mode log": {"f1": 0.6666666666666661, "precision": 0.4999999999999999, "recall": 0.9999999999999998, "threshold": 902.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.4444444444444441, "precision": 0.2857142857142857, "recall": 0.9999999999999996, "threshold": 902.0}}, "30": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.5833330154399992}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.5833330154399992}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.5833330154399992}}, "131": {"best f1 under pa": {"f1": 0.9393939393939388, "precision": 0.8857142857142857, "recall": 1.0, "threshold": 2.375}, "event-based f1 under pa with mode log": {"f1": 0.6923076923076917, "precision": 0.5294117647058824, "recall": 0.9999999999999998, "threshold": 2.375}, "event-based f1 under pa with mode squeeze": {"f1": 0.666666666666666, "precision": 0.9999999999999996, "recall": 0.4999999999999999, "threshold": 26.625}}, "125": {"best f1 under pa": {"f1": 0.9041095890410953, "precision": 0.9801980198019802, "recall": 0.8389830508474576, "threshold": 4.75}, "event-based f1 under pa with mode log": {"f1": 0.8571428571428564, "precision": 0.857142857142857, "recall": 0.857142857142857, "threshold": 4.75}, "event-based f1 under pa with mode squeeze": {"f1": 0.7692307692307686, "precision": 0.7142857142857142, "recall": 0.8333333333333333, "threshold": 4.75}}, "119": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 5636.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 5636.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 5636.0}}, "142": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 3.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 3.0}}, "156": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 839.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 839.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 839.0}}, "80": {"best f1 under pa": {"f1": 0.992030004688232, "precision": 0.9841860465116279, "recall": 1.0, "threshold": 21.520000000000003}, "event-based f1 under pa with mode log": {"f1": 0.8571428571428567, "precision": 0.84, "recall": 0.875, "threshold": 46.22}, "event-based f1 under pa with mode squeeze": {"f1": 0.7058823529411759, "precision": 0.5999999999999999, "recall": 0.857142857142857, "threshold": 46.22}}, "94": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 930.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 930.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 930.0}}, "181": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 725.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 725.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 725.0}}, "5": {"best f1 under pa": {"f1": 0.8865248226950349, "precision": 0.9191176470588235, "recall": 0.8561643835616438, "threshold": 10.666666984499999}, "event-based f1 under pa with mode log": {"f1": 0.7857142857142851, "precision": 0.846153846153846, "recall": 0.7333333333333333, "threshold": 15.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.666666666666666, "precision": 0.6666666666666665, "recall": 0.6666666666666665, "threshold": 15.0}}, "57": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2.6210523843800004}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2.6210523843800004}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 2.6210523843800004}}, "4": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 18.25}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 18.25}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 18.25}}, "194": {"best f1 under pa": {"f1": 0.9945553539019959, "precision": 0.9891696750902527, "recall": 1.0, "threshold": 2471.0}, "event-based f1 under pa with mode log": {"f1": 0.8235294117647052, "precision": 0.6999999999999998, "recall": 0.9999999999999999, "threshold": 2471.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666657, "precision": 0.9999999999999989, "recall": 0.4999999999999998, "threshold": 4324.0}}, "180": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 842.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 842.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 842.0}}, "95": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 879.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 879.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 879.0}}, "157": {"best f1 under pa": {"f1": 0.9784172661870497, "precision": 0.9577464788732394, "recall": 1.0, "threshold": 497.0}, "event-based f1 under pa with mode log": {"f1": 0.9473684210526311, "precision": 1.0, "recall": 0.9, "threshold": 1594.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9473684210526309, "precision": 0.9999999999999998, "recall": 0.8999999999999998, "threshold": 1594.0}}, "143": {"best f1 under pa": {"f1": 0.8396226415094334, "precision": 0.7235772357723578, "recall": 1.0, "threshold": 1.125}, "event-based f1 under pa with mode log": {"f1": 0.666666666666666, "precision": 0.6666666666666665, "recall": 0.6666666666666665, "threshold": 2.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.5999999999999994, "precision": 0.49999999999999994, "recall": 0.7499999999999999, "threshold": 2.5}}, "209": {"best f1 under pa": {"f1": 0.8540372670807448, "precision": 0.7452574525745257, "recall": 1.0, "threshold": 5.43}, "event-based f1 under pa with mode log": {"f1": 0.7857142857142851, "precision": 0.9166666666666665, "recall": 0.6875, "threshold": 19.07}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999993, "precision": 0.7999999999999998, "recall": 0.7999999999999998, "threshold": 19.07}}, "155": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 747.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 747.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 747.0}}, "97": {"best f1 under pa": {"f1": 0.9024390243902434, "precision": 0.8222222222222222, "recall": 1.0, "threshold": 4733.0}, "event-based f1 under pa with mode log": {"f1": 0.42857142857142805, "precision": 0.2727272727272727, "recall": 0.9999999999999997, "threshold": 4733.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.19999999999999973, "precision": 0.11111111111111109, "recall": 0.9999999999999989, "threshold": 4733.0}}, "169": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1116.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1116.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 1116.0}}, "83": {"best f1 under pa": {"f1": 0.9981851179673316, "precision": 0.9963768115942029, "recall": 1.0, "threshold": 1050.0}, "event-based f1 under pa with mode log": {"f1": 0.46666666666666623, "precision": 0.30434782608695654, "recall": 0.9999999999999999, "threshold": 1050.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.111111111111111, "precision": 0.058823529411764705, "recall": 0.9999999999999989, "threshold": 1050.0}}, "68": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2921.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2921.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 2921.0}}, "196": {"best f1 under pa": {"f1": 0.9620253164556958, "precision": 0.926829268292683, "recall": 1.0, "threshold": 4085.0}, "event-based f1 under pa with mode log": {"f1": 0.7272727272727267, "precision": 0.5714285714285714, "recall": 0.9999999999999998, "threshold": 4085.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.5714285714285708, "precision": 0.3999999999999999, "recall": 0.9999999999999996, "threshold": 4085.0}}, "182": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1986.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 1986.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 1986.0}}, "54": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 16.2000007629}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 16.2000007629}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 16.2000007629}}, "183": {"best f1 under pa": {"f1": 0.9775784753363223, "precision": 0.956140350877193, "recall": 1.0, "threshold": 1114.0}, "event-based f1 under pa with mode log": {"f1": 0.8484848484848478, "precision": 0.7368421052631579, "recall": 0.9999999999999999, "threshold": 1114.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.7368421052631573, "precision": 0.5833333333333333, "recall": 0.9999999999999999, "threshold": 1114.0}}, "69": {"best f1 under pa": {"f1": 0.9939024390243898, "precision": 0.9878787878787879, "recall": 1.0, "threshold": 17.21}, "event-based f1 under pa with mode log": {"f1": 0.9230769230769225, "precision": 0.857142857142857, "recall": 0.9999999999999999, "threshold": 17.21}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428564, "precision": 0.9999999999999997, "recall": 0.7499999999999999, "threshold": 22.749999999999996}}, "197": {"best f1 under pa": {"f1": 0.5379310344827581, "precision": 0.36792452830188677, "recall": 1.0, "threshold": 4064.0}, "event-based f1 under pa with mode log": {"f1": 0.19999999999999973, "precision": 0.11764705882352941, "recall": 0.6666666666666665, "threshold": 4489.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.11428571428571413, "precision": 0.0625, "recall": 0.6666666666666665, "threshold": 4489.0}}, "96": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4522.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 4522.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 4522.0}}, "168": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1084.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 1084.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 1084.0}}, "140": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2.941666662693}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2.941666662693}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 2.941666662693}}, "154": {"best f1 under pa": {"f1": 0.9906191369605999, "precision": 0.9814126394052045, "recall": 1.0, "threshold": 424.0}, "event-based f1 under pa with mode log": {"f1": 0.9354838709677413, "precision": 1.0, "recall": 0.8787878787878788, "threshold": 482.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9333333333333327, "precision": 0.9999999999999999, "recall": 0.875, "threshold": 482.0}}, "86": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 5415.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 5415.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 5415.0}}, "178": {"best f1 under pa": {"f1": 0.8372093023255809, "precision": 0.72, "recall": 1.0, "threshold": 48.56}, "event-based f1 under pa with mode log": {"f1": 0.29999999999999977, "precision": 0.17647058823529413, "recall": 0.9999999999999997, "threshold": 48.56}, "event-based f1 under pa with mode squeeze": {"f1": 0.12499999999999985, "precision": 0.06666666666666665, "recall": 0.9999999999999989, "threshold": 48.56}}, "150": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1211.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 1211.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 1211.0}}, "144": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.3125}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.3125}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.3125}}, "45": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2.64736819268}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2.64736819268}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 2.64736819268}}, "193": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 6027.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 6027.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 6027.0}}, "79": {"best f1 under pa": {"f1": 0.962790697674418, "precision": 0.9282511210762332, "recall": 1.0, "threshold": 8.600000000000001}, "event-based f1 under pa with mode log": {"f1": 0.9473684210526311, "precision": 1.0, "recall": 0.9, "threshold": 11.470000000000006}, "event-based f1 under pa with mode squeeze": {"f1": 0.9333333333333327, "precision": 0.9999999999999999, "recall": 0.8749999999999998, "threshold": 11.470000000000006}}, "187": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4334.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 4334.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 4334.0}}, "78": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 38.160000000000004}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 38.160000000000004}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 38.160000000000004}}, "186": {"best f1 under pa": {"f1": 0.991354466858789, "precision": 0.9828571428571429, "recall": 1.0, "threshold": 4510.0}, "event-based f1 under pa with mode log": {"f1": 0.8421052631578942, "precision": 0.7272727272727273, "recall": 1.0, "threshold": 4510.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.7272727272727265, "precision": 0.7999999999999998, "recall": 0.6666666666666665, "threshold": 5446.0}}, "192": {"best f1 under pa": {"f1": 0.9078014184397158, "precision": 0.8311688311688312, "recall": 1.0, "threshold": 4180.0}, "event-based f1 under pa with mode log": {"f1": 0.7368421052631573, "precision": 0.6999999999999998, "recall": 0.7777777777777777, "threshold": 5413.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.5999999999999994, "precision": 0.49999999999999994, "recall": 0.7499999999999999, "threshold": 5413.0}}, "50": {"best f1 under pa": {"f1": 0.9973890339425582, "precision": 0.9947916666666666, "recall": 1.0, "threshold": 1.4499998092699995}, "event-based f1 under pa with mode log": {"f1": 0.9473684210526309, "precision": 0.8999999999999998, "recall": 0.9999999999999998, "threshold": 1.4499998092699995}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428564, "precision": 0.7499999999999999, "recall": 0.9999999999999997, "threshold": 1.4499998092699995}}, "145": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.0625}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.0625}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.0625}}, "151": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 974.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 974.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 974.0}}, "179": {"best f1 under pa": {"f1": 0.919540229885057, "precision": 0.851063829787234, "recall": 1.0, "threshold": 40.26000000000002}, "event-based f1 under pa with mode log": {"f1": 0.5882352941176464, "precision": 0.41666666666666663, "recall": 0.9999999999999998, "threshold": 40.26000000000002}, "event-based f1 under pa with mode squeeze": {"f1": 0.3636363636363632, "precision": 0.22222222222222218, "recall": 0.9999999999999996, "threshold": 40.26000000000002}}, "93": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 817.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 817.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 817.0}}, "85": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2408.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2408.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2408.0}}, "147": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.5}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.5}}, "153": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1190.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1190.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 1190.0}}, "46": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2.4078946113600006}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2.4078946113600006}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 2.4078946113600006}}, "184": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 451.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 451.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 451.0}}, "190": {"best f1 under pa": {"f1": 0.99047619047619, "precision": 0.9811320754716981, "recall": 1.0, "threshold": 4563.0}, "event-based f1 under pa with mode log": {"f1": 0.9230769230769225, "precision": 0.857142857142857, "recall": 0.9999999999999999, "threshold": 4563.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428564, "precision": 0.7499999999999999, "recall": 0.9999999999999997, "threshold": 4563.0}}, "191": {"best f1 under pa": {"f1": 0.36286919831223596, "precision": 0.22994652406417113, "recall": 0.86, "threshold": 3586.0}, "event-based f1 under pa with mode log": {"f1": 0.0759493670886075, "precision": 0.04, "recall": 0.7499999999999999, "threshold": 3586.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.03973509933774829, "precision": 0.02040816326530612, "recall": 0.7499999999999999, "threshold": 3586.0}}, "185": {"best f1 under pa": {"f1": 0.9462365591397844, "precision": 0.9691629955947136, "recall": 0.9243697478991597, "threshold": 374.0}, "event-based f1 under pa with mode log": {"f1": 0.928571428571428, "precision": 1.0, "recall": 0.8666666666666667, "threshold": 624.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9285714285714279, "precision": 0.9999999999999999, "recall": 0.8666666666666666, "threshold": 624.0}}, "53": {"best f1 under pa": {"f1": 0.9879518072289151, "precision": 0.9761904761904762, "recall": 1.0, "threshold": 2.2500002384100006}, "event-based f1 under pa with mode log": {"f1": 0.9599999999999992, "precision": 0.9230769230769229, "recall": 0.9999999999999999, "threshold": 2.2500002384100006}, "event-based f1 under pa with mode squeeze": {"f1": 0.9230769230769225, "precision": 0.857142857142857, "recall": 0.9999999999999999, "threshold": 2.2500002384100006}}, "47": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2.849999904639999}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2.849999904639999}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 2.849999904639999}}, "152": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 490.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 490.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 490.0}}, "146": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.3125}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.3125}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.3125}}, "84": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4084.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 4084.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 4084.0}}, "201": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 12.089999999999996}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 12.089999999999996}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 12.089999999999996}}, "163": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 155.95}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 155.95}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 155.95}}, "177": {"best f1 under pa": {"f1": 0.8347826086956517, "precision": 0.7164179104477612, "recall": 1.0, "threshold": 22.640000000000015}, "event-based f1 under pa with mode log": {"f1": 0.486486486486486, "precision": 0.36, "recall": 0.7499999999999999, "threshold": 33.16999999999999}, "event-based f1 under pa with mode squeeze": {"f1": 0.3199999999999997, "precision": 0.2, "recall": 0.7999999999999998, "threshold": 33.16999999999999}}, "89": {"best f1 under pa": {"f1": 0.7333333333333328, "precision": 0.5789473684210527, "recall": 1.0, "threshold": 1476.0}, "event-based f1 under pa with mode log": {"f1": 0.19999999999999976, "precision": 0.1111111111111111, "recall": 0.9999999999999996, "threshold": 1476.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.111111111111111, "precision": 0.058823529411764705, "recall": 0.9999999999999989, "threshold": 1476.0}}, "62": {"best f1 under pa": {"f1": 0.9969604863221879, "precision": 0.9939393939393939, "recall": 1.0, "threshold": 1.3999998569500003}, "event-based f1 under pa with mode log": {"f1": 0.8888888888888881, "precision": 0.7999999999999998, "recall": 0.9999999999999998, "threshold": 1.3999998569500003}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666657, "precision": 0.4999999999999998, "recall": 0.9999999999999989, "threshold": 1.3999998569500003}}, "188": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3127.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 3127.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 3127.0}}, "176": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 201.3}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 201.3}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 201.3}}, "200": {"best f1 under pa": {"f1": 0.950819672131147, "precision": 0.90625, "recall": 1.0, "threshold": 25.28}, "event-based f1 under pa with mode log": {"f1": 0.6666666666666661, "precision": 0.49999999999999994, "recall": 0.9999999999999997, "threshold": 25.28}, "event-based f1 under pa with mode squeeze": {"f1": 0.39999999999999947, "precision": 0.24999999999999994, "recall": 0.9999999999999989, "threshold": 25.28}}, "202": {"best f1 under pa": {"f1": 0.9009009009009004, "precision": 0.819672131147541, "recall": 1.0, "threshold": 7.760000000000005}, "event-based f1 under pa with mode log": {"f1": 0.8749999999999992, "precision": 0.9999999999999999, "recall": 0.7777777777777777, "threshold": 10.859999999999996}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428564, "precision": 0.9999999999999997, "recall": 0.7499999999999999, "threshold": 10.859999999999996}}, "174": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 235.04}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 235.04}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 235.04}}, "160": {"best f1 under pa": {"f1": 0.9952606635071085, "precision": 0.9905660377358491, "recall": 1.0, "threshold": 424.0}, "event-based f1 under pa with mode log": {"f1": 0.9583333333333328, "precision": 0.92, "recall": 1.0, "threshold": 424.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9523809523809517, "precision": 0.9999999999999998, "recall": 0.909090909090909, "threshold": 724.0}}, "148": {"best f1 under pa": {"f1": 0.9898989898989894, "precision": 0.98, "recall": 1.0, "threshold": 1.875}, "event-based f1 under pa with mode log": {"f1": 0.9382716049382711, "precision": 0.8837209302325582, "recall": 1.0, "threshold": 1.875}, "event-based f1 under pa with mode squeeze": {"f1": 0.8780487804878044, "precision": 0.782608695652174, "recall": 1.0, "threshold": 1.875}}, "49": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.6500000953600007}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 1.6500000953600007}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 1.6500000953600007}}, "61": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1.10000014305}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 1.10000014305}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 1.10000014305}}, "60": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 23.46842145923}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 23.46842145923}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 23.46842145923}}, "74": {"best f1 under pa": {"f1": 0.9455184534270645, "precision": 0.8966666666666666, "recall": 1.0, "threshold": 5.880000000000003}, "event-based f1 under pa with mode log": {"f1": 0.7368421052631573, "precision": 0.6666666666666666, "recall": 0.8235294117647058, "threshold": 9.719999999999999}, "event-based f1 under pa with mode squeeze": {"f1": 0.666666666666666, "precision": 0.6666666666666665, "recall": 0.6666666666666665, "threshold": 14.469999999999999}}, "48": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 23.99999904637}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 23.99999904637}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 23.99999904637}}, "149": {"best f1 under pa": {"f1": 0.9993108201240518, "precision": 0.9986225895316805, "recall": 1.0, "threshold": 0.8125}, "event-based f1 under pa with mode log": {"f1": 0.9919999999999993, "precision": 0.9841269841269841, "recall": 1.0, "threshold": 0.8125}, "event-based f1 under pa with mode squeeze": {"f1": 0.9824561403508766, "precision": 0.9655172413793104, "recall": 1.0, "threshold": 0.8125}}, "161": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 735.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 735.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 735.0}}, "203": {"best f1 under pa": {"f1": 0.9534619750283763, "precision": 0.911062906724512, "recall": 1.0, "threshold": 6.34}, "event-based f1 under pa with mode log": {"f1": 0.8421052631578941, "precision": 0.9411764705882353, "recall": 0.7619047619047619, "threshold": 9.869999999999997}, "event-based f1 under pa with mode squeeze": {"f1": 0.8749999999999993, "precision": 0.8749999999999998, "recall": 0.8749999999999998, "threshold": 9.869999999999997}}, "207": {"best f1 under pa": {"f1": 0.973262032085561, "precision": 0.9479166666666666, "recall": 1.0, "threshold": 18.839999999999996}, "event-based f1 under pa with mode log": {"f1": 0.8648648648648642, "precision": 0.7619047619047619, "recall": 1.0, "threshold": 18.839999999999996}, "event-based f1 under pa with mode squeeze": {"f1": 0.8333333333333327, "precision": 0.9999999999999998, "recall": 0.7142857142857142, "threshold": 24.1}}, "159": {"best f1 under pa": {"f1": 0.9941860465116275, "precision": 0.9884393063583815, "recall": 1.0, "threshold": 761.5}, "event-based f1 under pa with mode log": {"f1": 0.9615384615384609, "precision": 0.9259259259259259, "recall": 1.0, "threshold": 761.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.9230769230769225, "precision": 0.857142857142857, "recall": 0.9999999999999999, "threshold": 761.5}}, "171": {"best f1 under pa": {"f1": 0.996563573883161, "precision": 0.9931506849315068, "recall": 1.0, "threshold": 700.0}, "event-based f1 under pa with mode log": {"f1": 0.9756097560975604, "precision": 0.9523809523809523, "recall": 1.0, "threshold": 700.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9523809523809517, "precision": 0.909090909090909, "recall": 0.9999999999999998, "threshold": 700.0}}, "165": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 146.76}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 146.76}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 146.76}}, "70": {"best f1 under pa": {"f1": 0.9924433249370272, "precision": 0.985, "recall": 1.0, "threshold": 13.440000000000005}, "event-based f1 under pa with mode log": {"f1": 0.8888888888888883, "precision": 0.7999999999999999, "recall": 0.9999999999999999, "threshold": 13.440000000000005}, "event-based f1 under pa with mode squeeze": {"f1": 0.7499999999999994, "precision": 0.7499999999999999, "recall": 0.7499999999999999, "threshold": 20.209999999999994}}, "58": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2.51315784454}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2.51315784454}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 2.51315784454}}, "59": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2.0999999046300015}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2.0999999046300015}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2.0999999046300015}}, "65": {"best f1 under pa": {"f1": 0.9883990719257535, "precision": 0.9770642201834863, "recall": 1.0, "threshold": 2104.0}, "event-based f1 under pa with mode log": {"f1": 0.6153846153846148, "precision": 0.44444444444444436, "recall": 0.9999999999999998, "threshold": 2104.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.2857142857142853, "precision": 0.16666666666666663, "recall": 0.9999999999999989, "threshold": 2104.0}}, "71": {"best f1 under pa": {"f1": 0.9118683901292591, "precision": 0.9773299748110831, "recall": 0.8546255506607929, "threshold": 8.61}, "event-based f1 under pa with mode log": {"f1": 0.8181818181818177, "precision": 0.75, "recall": 0.9, "threshold": 8.61}, "event-based f1 under pa with mode squeeze": {"f1": 0.6428571428571422, "precision": 0.5, "recall": 0.8999999999999998, "threshold": 8.61}}, "164": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 199.37}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 199.37}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 199.37}}, "170": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1629.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 1629.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 1629.0}}, "158": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1406.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1406.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 1406.0}}, "204": {"best f1 under pa": {"f1": 0.9615384615384609, "precision": 0.9259259259259259, "recall": 1.0, "threshold": 19.61}, "event-based f1 under pa with mode log": {"f1": 0.7499999999999992, "precision": 0.9999999999999997, "recall": 0.5999999999999999, "threshold": 29.110000000000007}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666657, "precision": 0.9999999999999989, "recall": 0.4999999999999998, "threshold": 29.110000000000007}}, "166": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 125.04}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 125.04}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 125.04}}, "98": {"best f1 under pa": {"f1": 0.9333333333333328, "precision": 0.875, "recall": 1.0, "threshold": 3293.0}, "event-based f1 under pa with mode log": {"f1": 0.5999999999999993, "precision": 0.4285714285714285, "recall": 0.9999999999999997, "threshold": 3293.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.3333333333333329, "precision": 0.19999999999999996, "recall": 0.9999999999999989, "threshold": 3293.0}}, "172": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 620.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 620.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 620.0}}, "199": {"best f1 under pa": {"f1": 0.996563573883161, "precision": 0.9931506849315068, "recall": 1.0, "threshold": 22.21}, "event-based f1 under pa with mode log": {"f1": 0.9629629629629624, "precision": 0.9285714285714285, "recall": 0.9999999999999999, "threshold": 22.21}, "event-based f1 under pa with mode squeeze": {"f1": 0.9090909090909084, "precision": 0.8333333333333333, "recall": 0.9999999999999998, "threshold": 22.21}}, "67": {"best f1 under pa": {"f1": 0.9863013698630132, "precision": 0.972972972972973, "recall": 1.0, "threshold": 2390.0}, "event-based f1 under pa with mode log": {"f1": 0.8999999999999991, "precision": 0.818181818181818, "recall": 0.9999999999999998, "threshold": 2390.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999994, "precision": 0.6666666666666665, "recall": 0.9999999999999998, "threshold": 2390.0}}, "73": {"best f1 under pa": {"f1": 0.9943714821763597, "precision": 0.9888059701492538, "recall": 1.0, "threshold": 15.529999999999994}, "event-based f1 under pa with mode log": {"f1": 0.8799999999999992, "precision": 0.7857142857142856, "recall": 0.9999999999999999, "threshold": 15.529999999999994}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999992, "precision": 0.9999999999999996, "recall": 0.6666666666666665, "threshold": 39.82000000000001}}, "9": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3186.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 3186.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 3186.0}}, "72": {"best f1 under pa": {"f1": 0.9237472766884526, "precision": 0.8582995951417004, "recall": 1.0, "threshold": 8.07}, "event-based f1 under pa with mode log": {"f1": 0.8695652173913037, "precision": 0.9999999999999998, "recall": 0.7692307692307692, "threshold": 34.190000000000005}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428564, "precision": 0.9999999999999997, "recall": 0.7499999999999999, "threshold": 34.190000000000005}}, "198": {"best f1 under pa": {"f1": 0.99047619047619, "precision": 0.9811320754716981, "recall": 1.0, "threshold": 19.479999999999997}, "event-based f1 under pa with mode log": {"f1": 0.9090909090909084, "precision": 0.8333333333333333, "recall": 0.9999999999999998, "threshold": 19.479999999999997}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999992, "precision": 0.6666666666666665, "recall": 0.9999999999999996, "threshold": 19.479999999999997}}, "66": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4888.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 4888.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 4888.0}}, "173": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 563.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 563.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 563.0}}, "167": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 47.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 47.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 47.0}}, "99": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 5125.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 5125.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 5125.0}}, "205": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 62.530000000000015}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 62.530000000000015}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 62.530000000000015}}, "128": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3.75}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 3.75}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 3.75}}, "29": {"best f1 under pa": {"f1": 0.9840810419681615, "precision": 0.9686609686609686, "recall": 1.0, "threshold": 1.66666662693}, "event-based f1 under pa with mode log": {"f1": 0.8607594936708856, "precision": 0.7555555555555555, "recall": 1.0, "threshold": 1.66666662693}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999995, "precision": 0.857142857142857, "recall": 0.75, "threshold": 2.9999997615799994}}, "15": {"best f1 under pa": {"f1": 0.9704510108864691, "precision": 0.9425981873111783, "recall": 1.0, "threshold": 7.589999999999996}, "event-based f1 under pa with mode log": {"f1": 0.7878787878787874, "precision": 0.8125, "recall": 0.7647058823529411, "threshold": 14.21}, "event-based f1 under pa with mode squeeze": {"f1": 0.7499999999999992, "precision": 0.9999999999999997, "recall": 0.5999999999999999, "threshold": 20.980000000000004}}, "14": {"best f1 under pa": {"f1": 0.9736842105263153, "precision": 0.9487179487179487, "recall": 1.0, "threshold": 6.809999999999999}, "event-based f1 under pa with mode log": {"f1": 0.9374999999999993, "precision": 0.9999999999999999, "recall": 0.8823529411764706, "threshold": 11.389999999999993}, "event-based f1 under pa with mode squeeze": {"f1": 0.9333333333333327, "precision": 0.9999999999999999, "recall": 0.8749999999999998, "threshold": 11.389999999999993}}, "28": {"best f1 under pa": {"f1": 0.9953917050691239, "precision": 0.9908256880733946, "recall": 1.0, "threshold": 2.5}, "event-based f1 under pa with mode log": {"f1": 0.9743589743589738, "precision": 0.95, "recall": 1.0, "threshold": 2.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.9499999999999995, "precision": 0.9047619047619048, "recall": 1.0, "threshold": 2.5}}, "101": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 5918.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 5918.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 5918.0}}, "117": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 14129.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 14129.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 14129.0}}, "103": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1508.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1508.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 1508.0}}, "16": {"best f1 under pa": {"f1": 0.9829059829059823, "precision": 0.9663865546218487, "recall": 1.0, "threshold": 21.1}, "event-based f1 under pa with mode log": {"f1": 0.7999999999999994, "precision": 0.6666666666666665, "recall": 0.9999999999999998, "threshold": 21.1}, "event-based f1 under pa with mode squeeze": {"f1": 0.5999999999999993, "precision": 0.4285714285714285, "recall": 0.9999999999999997, "threshold": 21.1}}, "17": {"best f1 under pa": {"f1": 0.9851485148514846, "precision": 0.9707317073170731, "recall": 1.0, "threshold": 8.29}, "event-based f1 under pa with mode log": {"f1": 0.7999999999999994, "precision": 0.6666666666666666, "recall": 0.9999999999999999, "threshold": 8.29}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666661, "precision": 0.5999999999999999, "recall": 0.7499999999999999, "threshold": 17.099999999999998}}, "102": {"best f1 under pa": {"f1": 0.9844559585492223, "precision": 0.9693877551020408, "recall": 1.0, "threshold": 949.0}, "event-based f1 under pa with mode log": {"f1": 0.9032258064516123, "precision": 0.8235294117647058, "recall": 0.9999999999999999, "threshold": 949.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.8235294117647052, "precision": 0.6999999999999998, "recall": 0.9999999999999999, "threshold": 949.0}}, "116": {"best f1 under pa": {"f1": 0.998855835240274, "precision": 0.9977142857142857, "recall": 1.0, "threshold": 1054.0}, "event-based f1 under pa with mode log": {"f1": 0.5833333333333328, "precision": 0.4117647058823529, "recall": 0.9999999999999999, "threshold": 1054.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.1666666666666665, "precision": 0.0909090909090909, "recall": 0.9999999999999989, "threshold": 1054.0}}, "112": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 575.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 575.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 575.0}}, "106": {"best f1 under pa": {"f1": 0.9915254237288129, "precision": 0.9831932773109243, "recall": 1.0, "threshold": 497.0}, "event-based f1 under pa with mode log": {"f1": 0.9473684210526311, "precision": 0.9, "recall": 1.0, "threshold": 497.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9411764705882346, "precision": 0.9999999999999998, "recall": 0.8888888888888887, "threshold": 958.0}}, "13": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 11.739999999999995}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 11.739999999999995}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 11.739999999999995}}, "12": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 17.189999999999998}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 17.189999999999998}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 17.189999999999998}}, "107": {"best f1 under pa": {"f1": 0.9955357142857139, "precision": 0.9911111111111112, "recall": 1.0, "threshold": 548.0}, "event-based f1 under pa with mode log": {"f1": 0.9677419354838704, "precision": 0.9375, "recall": 1.0, "threshold": 548.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9333333333333327, "precision": 0.875, "recall": 0.9999999999999999, "threshold": 548.0}}, "113": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 556.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 556.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 556.0}}, "105": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 808.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 808.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 808.0}}, "111": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1011.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 1011.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 1011.0}}, "10": {"best f1 under pa": {"f1": 0.986666666666666, "precision": 0.9736842105263158, "recall": 1.0, "threshold": 1259.0}, "event-based f1 under pa with mode log": {"f1": 0.7499999999999992, "precision": 0.5999999999999999, "recall": 0.9999999999999997, "threshold": 1259.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.49999999999999933, "precision": 0.33333333333333326, "recall": 0.9999999999999989, "threshold": 1259.0}}, "38": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 9.82}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 9.82}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 9.82}}, "110": {"best f1 under pa": {"f1": 0.9257142857142852, "precision": 0.8617021276595744, "recall": 1.0, "threshold": 419.5}, "event-based f1 under pa with mode log": {"f1": 0.8888888888888881, "precision": 0.9999999999999998, "recall": 0.7999999999999998, "threshold": 1163.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.8888888888888881, "precision": 0.9999999999999998, "recall": 0.7999999999999998, "threshold": 1163.0}}, "104": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 958.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 958.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 958.0}}}