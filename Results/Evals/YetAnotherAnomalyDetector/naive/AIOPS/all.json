{"c02607e8-7399-3dde-9d28-8a8da5e5d251": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 0.0828172942023956}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 0.0828172942023956}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 0.0828172942023956}}, "a8c06b47-cc41-3738-9110-12df0ee4c721": {"best f1 under pa": {"f1": 0.9258160237388718, "precision": 0.9873417721518988, "recall": 0.8715083798882681, "threshold": 8.138146086999999}, "event-based f1 under pa with mode log": {"f1": 0.9047619047619042, "precision": 0.9047619047619048, "recall": 0.9047619047619048, "threshold": 8.138146086999999}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428563, "precision": 0.818181818181818, "recall": 0.8999999999999998, "threshold": 8.138146086999999}}, "6d1114ae-be04-3c46-b5aa-be1a003a57cd": {"best f1 under pa": {"f1": 0.7952713594841477, "precision": 0.8851674641148325, "recall": 0.7219512195121951, "threshold": 25.67999999999998}, "event-based f1 under pa with mode log": {"f1": 0.854545454545454, "precision": 0.9306930693069307, "recall": 0.7899159663865546, "threshold": 37.81}, "event-based f1 under pa with mode squeeze": {"f1": 0.8631578947368415, "precision": 0.9761904761904762, "recall": 0.7735849056603774, "threshold": 44.81}}, "1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0": {"best f1 under pa": {"f1": 0.9047900650502656, "precision": 0.9858247422680413, "recall": 0.8360655737704918, "threshold": 8.630000000000003}, "event-based f1 under pa with mode log": {"f1": 0.9037656903765685, "precision": 0.9818181818181818, "recall": 0.8372093023255814, "threshold": 10.869999999999997}, "event-based f1 under pa with mode squeeze": {"f1": 0.8952380952380946, "precision": 0.9591836734693877, "recall": 0.8392857142857143, "threshold": 10.869999999999997}}, "54350a12-7a9d-3ca8-b81f-f886b9d156fd": {"best f1 under pa": {"f1": 0.8785046728971958, "precision": 0.7833333333333333, "recall": 1.0, "threshold": 2.9703063108179997}, "event-based f1 under pa with mode log": {"f1": 0.5185185185185179, "precision": 0.5833333333333333, "recall": 0.4666666666666666, "threshold": 5.95983577779}, "event-based f1 under pa with mode squeeze": {"f1": 0.49999999999999944, "precision": 0.9999999999999996, "recall": 0.33333333333333326, "threshold": 13.4049921034}}, "301c70d8-1630-35ac-8f96-bc1b6f4359ea": {"best f1 under pa": {"f1": 0.9384404924760597, "precision": 0.9346049046321526, "recall": 0.9423076923076923, "threshold": 0.038112298947415496}, "event-based f1 under pa with mode log": {"f1": 0.8749999999999994, "precision": 0.9032258064516129, "recall": 0.8484848484848485, "threshold": 0.04892344144747096}, "event-based f1 under pa with mode squeeze": {"f1": 0.8358208955223876, "precision": 0.8235294117647058, "recall": 0.8484848484848485, "threshold": 0.04892344144747096}}, "8723f0fb-eaef-32e6-b372-6034c9c04b80": {"best f1 under pa": {"f1": 0.8855098389982105, "precision": 0.8878923766816144, "recall": 0.8831400535236396, "threshold": 26.710000000000008}, "event-based f1 under pa with mode log": {"f1": 0.8118081180811803, "precision": 0.8270676691729323, "recall": 0.7971014492753623, "threshold": 34.21000000000001}, "event-based f1 under pa with mode squeeze": {"f1": 0.7889908256880728, "precision": 0.9148936170212766, "recall": 0.6935483870967742, "threshold": 44.879999999999995}}, "e0747cad-8dc8-38a9-a9ab-855b61f5551d": {"best f1 under pa": {"f1": 0.9969969969969964, "precision": 0.9940119760479041, "recall": 1.0, "threshold": 0.6625781919999998}, "event-based f1 under pa with mode log": {"f1": 0.9777777777777772, "precision": 0.9565217391304348, "recall": 1.0, "threshold": 0.6625781919999998}, "event-based f1 under pa with mode squeeze": {"f1": 0.9523809523809517, "precision": 0.909090909090909, "recall": 0.9999999999999998, "threshold": 0.6625781919999998}}, "57051487-3a40-3828-9084-a12f7f23ee38": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 545.25}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 545.25}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 545.25}}, "da10a69f-d836-3baa-ad40-3e548ecf1fbd": {"best f1 under pa": {"f1": 0.8784741144414163, "precision": 0.8830941163580585, "recall": 0.8739022010191911, "threshold": 0.0645940766560261}, "event-based f1 under pa with mode log": {"f1": 0.6568047337278101, "precision": 0.8604651162790697, "recall": 0.5311004784688995, "threshold": 0.1537318405162}, "event-based f1 under pa with mode squeeze": {"f1": 0.6330935251798556, "precision": 0.7096774193548387, "recall": 0.5714285714285714, "threshold": 0.1537318405162}}, "ab216663-dcc2-3a24-b1ee-2c3e550e06c9": {"best f1 under pa": {"f1": 0.5644916540212438, "precision": 0.8051948051948052, "recall": 0.43457943925233644, "threshold": 0.07792669434000032}, "event-based f1 under pa with mode log": {"f1": 0.7450980392156857, "precision": 1.0, "recall": 0.59375, "threshold": 0.18655820031499998}, "event-based f1 under pa with mode squeeze": {"f1": 0.7826086956521733, "precision": 0.9999999999999998, "recall": 0.6428571428571428, "threshold": 0.18655820031499998}}, "f0932edd-6400-3e63-9559-0a9860a1baa9": {"best f1 under pa": {"f1": 0.9519216717813819, "precision": 0.9869696969696969, "recall": 0.9192774484899803, "threshold": 10.375}, "event-based f1 under pa with mode log": {"f1": 0.8320610687022895, "precision": 0.9316239316239316, "recall": 0.7517241379310344, "threshold": 13.1875}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999994, "precision": 0.8518518518518519, "recall": 0.7540983606557377, "threshold": 13.1875}}, "0efb375b-b902-3661-ab23-9a0bb799f4e3": {"best f1 under pa": {"f1": 0.990196078431372, "precision": 0.9805825242718447, "recall": 1.0, "threshold": 0.0349201827097364}, "event-based f1 under pa with mode log": {"f1": 0.9285714285714279, "precision": 0.8666666666666666, "recall": 0.9999999999999999, "threshold": 0.0349201827097364}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428564, "precision": 0.7499999999999999, "recall": 0.9999999999999999, "threshold": 0.0349201827097364}}, "c69a50cf-ee03-3bd7-831e-407d36c7ee91": {"best f1 under pa": {"f1": 0.9155227032734946, "precision": 0.9687150837988827, "recall": 0.8678678678678678, "threshold": 7.529999999999998}, "event-based f1 under pa with mode log": {"f1": 0.906976744186046, "precision": 0.9669421487603306, "recall": 0.8540145985401459, "threshold": 9.439999999999998}, "event-based f1 under pa with mode squeeze": {"f1": 0.8947368421052626, "precision": 0.9272727272727272, "recall": 0.864406779661017, "threshold": 9.439999999999998}}, "ba5f3328-9f3f-3ff5-a683-84437d16d554": {"best f1 under pa": {"f1": 0.9846668525229991, "precision": 0.9925530420120837, "recall": 0.9769049923938598, "threshold": 22.870000000000005}, "event-based f1 under pa with mode log": {"f1": 0.9268292682926824, "precision": 0.954337899543379, "recall": 0.9008620689655172, "threshold": 27.860000000000014}, "event-based f1 under pa with mode squeeze": {"f1": 0.8969696969696965, "precision": 0.9367088607594937, "recall": 0.8604651162790697, "threshold": 31.120000000000005}}, "adb2fde9-8589-3f5b-a410-5fe14386c7af": {"best f1 under pa": {"f1": 0.8251121076233179, "precision": 0.7022900763358778, "recall": 1.0, "threshold": 5.829999999999998}, "event-based f1 under pa with mode log": {"f1": 0.9142857142857137, "precision": 0.9119170984455959, "recall": 0.9166666666666666, "threshold": 9.869999999999997}, "event-based f1 under pa with mode squeeze": {"f1": 0.8961038961038955, "precision": 1.0, "recall": 0.8117647058823529, "threshold": 12.900000000000002}}, "a07ac296-de40-3a7c-8df3-91f642cc14d0": {"best f1 under pa": {"f1": 0.8298109010011118, "precision": 0.9841688654353562, "recall": 0.7173076923076923, "threshold": 26.75999999999999}, "event-based f1 under pa with mode log": {"f1": 0.7670250896057342, "precision": 0.9304347826086956, "recall": 0.6524390243902439, "threshold": 31.789999999999992}, "event-based f1 under pa with mode squeeze": {"f1": 0.7540983606557372, "precision": 0.8518518518518519, "recall": 0.6764705882352942, "threshold": 31.789999999999992}}, "4d2af31a-9916-3d9f-8a8e-8a268a48c095": {"best f1 under pa": {"f1": 0.9829915752662527, "precision": 0.9847133757961783, "recall": 0.9812757854649318, "threshold": 1.33333337307}, "event-based f1 under pa with mode log": {"f1": 0.7343749999999993, "precision": 0.7230769230769231, "recall": 0.746031746031746, "threshold": 1.5421049594900005}, "event-based f1 under pa with mode squeeze": {"f1": 0.6470588235294112, "precision": 0.9999999999999999, "recall": 0.4782608695652174, "threshold": 2.0}}, "ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa": {"best f1 under pa": {"f1": 0.9784660766961646, "precision": 0.9773129051266942, "recall": 0.9796219728292971, "threshold": 0.8000001907300005}, "event-based f1 under pa with mode log": {"f1": 0.8440366972477058, "precision": 0.9787234042553191, "recall": 0.7419354838709677, "threshold": 1.33333337307}, "event-based f1 under pa with mode squeeze": {"f1": 0.8108108108108103, "precision": 0.9375, "recall": 0.7142857142857143, "threshold": 1.33333337307}}, "55f8b8b8-b659-38df-b3df-e4a5a8a54bc9": {"best f1 under pa": {"f1": 0.9832909832909827, "precision": 0.9803469983110702, "recall": 0.986252703120173, "threshold": 21.849999999999994}, "event-based f1 under pa with mode log": {"f1": 0.8972972972972968, "precision": 0.9540229885057471, "recall": 0.8469387755102041, "threshold": 30.47999999999999}, "event-based f1 under pa with mode squeeze": {"f1": 0.8888888888888883, "precision": 0.8888888888888888, "recall": 0.8888888888888888, "threshold": 30.47999999999999}}, "431a8542-c468-3988-a508-3afd06a218da": {"best f1 under pa": {"f1": 0.993561278863232, "precision": 0.9953291814946619, "recall": 0.9917996453900709, "threshold": 1.0588233470899997}, "event-based f1 under pa with mode log": {"f1": 0.9741060419235507, "precision": 0.9777227722772277, "recall": 0.9705159705159705, "threshold": 1.25}, "event-based f1 under pa with mode squeeze": {"f1": 0.9612403100775188, "precision": 0.9538461538461539, "recall": 0.96875, "threshold": 1.25}}, "42d6616d-c9c5-370a-a8ba-17ead74f3114": {"best f1 under pa": {"f1": 0.8057354301572613, "precision": 0.8248106060606061, "recall": 0.7875226039783002, "threshold": 6.830000000000002}, "event-based f1 under pa with mode log": {"f1": 0.9071428571428566, "precision": 0.9694656488549618, "recall": 0.8523489932885906, "threshold": 14.509999999999998}, "event-based f1 under pa with mode squeeze": {"f1": 0.9193548387096769, "precision": 0.9344262295081968, "recall": 0.9047619047619048, "threshold": 14.509999999999998}}, "43115f2a-baeb-3b01-96f7-4ea14188343c": {"best f1 under pa": {"f1": 0.9198489299202681, "precision": 0.9217830109335576, "recall": 0.9179229480737019, "threshold": 5.359999999999999}, "event-based f1 under pa with mode log": {"f1": 0.6868686868686864, "precision": 0.8947368421052632, "recall": 0.5573770491803278, "threshold": 9.359999999999996}, "event-based f1 under pa with mode squeeze": {"f1": 0.5897435897435892, "precision": 0.8518518518518519, "recall": 0.45098039215686275, "threshold": 10.009999999999998}}, "9c639a46-34c8-39bc-aaf0-9144b37adfc8": {"best f1 under pa": {"f1": 0.8641785302039241, "precision": 0.910786699107867, "recall": 0.822108345534407, "threshold": 4.93}, "event-based f1 under pa with mode log": {"f1": 0.6078431372549014, "precision": 0.6888888888888889, "recall": 0.543859649122807, "threshold": 5.849999999999998}, "event-based f1 under pa with mode squeeze": {"f1": 0.4874999999999995, "precision": 0.48148148148148145, "recall": 0.4936708860759494, "threshold": 5.849999999999998}}, "05f10d3a-239c-3bef-9bdc-a2feeb0037aa": {"best f1 under pa": {"f1": 0.9865125240847779, "precision": 0.9899458623356535, "recall": 0.9831029185867896, "threshold": 8.440000000000005}, "event-based f1 under pa with mode log": {"f1": 0.9526813880126177, "precision": 0.9207317073170732, "recall": 0.9869281045751634, "threshold": 8.440000000000005}, "event-based f1 under pa with mode squeeze": {"f1": 0.924369747899159, "precision": 0.9821428571428571, "recall": 0.873015873015873, "threshold": 10.420000000000002}}, "7103fa0f-cac4-314f-addc-866190247439": {"best f1 under pa": {"f1": 0.9038461538461533, "precision": 0.9724137931034482, "recall": 0.844311377245509, "threshold": 127.0}, "event-based f1 under pa with mode log": {"f1": 0.8767123287671228, "precision": 1.0, "recall": 0.7804878048780488, "threshold": 188.800003052}, "event-based f1 under pa with mode squeeze": {"f1": 0.8648648648648642, "precision": 1.0, "recall": 0.7619047619047619, "threshold": 188.800003052}}, "6efa3a07-4544-34a0-b921-a155bd1a05e8": {"best f1 under pa": {"f1": 0.8282726892513587, "precision": 0.8175363276089829, "recall": 0.8392947957280895, "threshold": 17.08}, "event-based f1 under pa with mode log": {"f1": 0.347083926031294, "precision": 0.32020997375328086, "recall": 0.37888198757763975, "threshold": 30.429999999999993}, "event-based f1 under pa with mode squeeze": {"f1": 0.20183486238532064, "precision": 0.15865384615384615, "recall": 0.2773109243697479, "threshold": 35.150000000000006}}, "6a757df4-95e5-3357-8406-165e2bd49360": {"best f1 under pa": {"f1": 0.8434677904876574, "precision": 0.775103734439834, "recall": 0.9250577748431825, "threshold": 0.8888889402119999}, "event-based f1 under pa with mode log": {"f1": 0.5624999999999996, "precision": 0.9, "recall": 0.4090909090909091, "threshold": 3.55555546283}, "event-based f1 under pa with mode squeeze": {"f1": 0.6086956521739124, "precision": 0.7777777777777777, "recall": 0.49999999999999994, "threshold": 3.55555546283}}, "847e8ecc-f8d2-3a93-9107-f367a0aab37d": {"best f1 under pa": {"f1": 0.9488117001828148, "precision": 0.9292748433303492, "recall": 0.969187675070028, "threshold": 7.899999999999999}, "event-based f1 under pa with mode log": {"f1": 0.8978102189781016, "precision": 0.9044117647058824, "recall": 0.8913043478260869, "threshold": 9.719999999999999}, "event-based f1 under pa with mode squeeze": {"f1": 0.8952380952380946, "precision": 0.9591836734693877, "recall": 0.8392857142857143, "threshold": 13.080000000000005}}}