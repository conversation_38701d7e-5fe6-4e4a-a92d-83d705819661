{"121": {"best f1 under pa": {"f1": 0.956521739130434, "precision": 0.9166666666666665, "recall": 0.9999999999999999, "threshold": 9.375}, "event-based f1 under pa with mode log": {"f1": 0.7999999999999992, "precision": 0.6666666666666665, "recall": 0.9999999999999996, "threshold": 9.375}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666657, "precision": 0.4999999999999998, "recall": 0.9999999999999989, "threshold": 9.375}}, "135": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 34.625}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 34.625}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 34.625}}, "109": {"best f1 under pa": {"f1": 0.9245283018867919, "precision": 1.0, "recall": 0.8596491228070176, "threshold": 3474.0}, "event-based f1 under pa with mode log": {"f1": 0.9333333333333327, "precision": 0.9999999999999999, "recall": 0.875, "threshold": 3474.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9333333333333327, "precision": 0.9999999999999999, "recall": 0.8749999999999998, "threshold": 3474.0}}, "34": {"best f1 under pa": {"f1": 0.9976580796252922, "precision": 0.9953271028037384, "recall": 1.0, "threshold": 67.25}, "event-based f1 under pa with mode log": {"f1": 0.9523809523809517, "precision": 0.909090909090909, "recall": 0.9999999999999998, "threshold": 67.25}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428564, "precision": 0.7499999999999999, "recall": 0.9999999999999997, "threshold": 67.25}}, "35": {"best f1 under pa": {"f1": 0.9923195084485401, "precision": 0.9847560975609756, "recall": 1.0, "threshold": 46.730000000000004}, "event-based f1 under pa with mode log": {"f1": 0.915254237288135, "precision": 0.84375, "recall": 1.0, "threshold": 46.730000000000004}, "event-based f1 under pa with mode squeeze": {"f1": 0.8888888888888881, "precision": 0.9999999999999998, "recall": 0.7999999999999998, "threshold": 49.84}}, "21": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 10049.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 10049.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 10049.0}}, "108": {"best f1 under pa": {"f1": 0.9909909909909903, "precision": 0.9821428571428571, "recall": 1.0, "threshold": 2814.0}, "event-based f1 under pa with mode log": {"f1": 0.9411764705882346, "precision": 0.8888888888888887, "recall": 0.9999999999999998, "threshold": 2814.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.8888888888888881, "precision": 0.7999999999999998, "recall": 0.9999999999999998, "threshold": 2814.0}}, "134": {"best f1 under pa": {"f1": 0.9873417721518981, "precision": 0.975, "recall": 1.0, "threshold": 68.75}, "event-based f1 under pa with mode log": {"f1": 0.8799999999999993, "precision": 0.9166666666666665, "recall": 0.846153846153846, "threshold": 69.6875}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999993, "precision": 0.7999999999999998, "recall": 0.7999999999999998, "threshold": 69.6875}}, "120": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 7.1875}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 7.1875}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 7.1875}}, "136": {"best f1 under pa": {"f1": 0.913385826771653, "precision": 0.9206349206349206, "recall": 0.90625, "threshold": 23.75}, "event-based f1 under pa with mode log": {"f1": 0.777777777777777, "precision": 0.9999999999999999, "recall": 0.6363636363636362, "threshold": 25.25}, "event-based f1 under pa with mode squeeze": {"f1": 0.7499999999999992, "precision": 0.9999999999999997, "recall": 0.5999999999999999, "threshold": 25.25}}, "122": {"best f1 under pa": {"f1": 0.9872611464968147, "precision": 1.0, "recall": 0.9748427672955975, "threshold": 8.9375}, "event-based f1 under pa with mode log": {"f1": 0.9230769230769225, "precision": 0.9999999999999999, "recall": 0.857142857142857, "threshold": 8.9375}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999992, "precision": 0.9999999999999996, "recall": 0.6666666666666665, "threshold": 8.9375}}, "37": {"best f1 under pa": {"f1": 0.9812332439678279, "precision": 1.0, "recall": 0.9631578947368421, "threshold": 123.09}, "event-based f1 under pa with mode log": {"f1": 0.9499999999999995, "precision": 1.0, "recall": 0.9047619047619048, "threshold": 123.09}, "event-based f1 under pa with mode squeeze": {"f1": 0.9411764705882346, "precision": 0.9999999999999998, "recall": 0.8888888888888887, "threshold": 123.09}}, "23": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 16470.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 16470.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 16470.0}}, "22": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 13746.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 13746.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 13746.0}}, "36": {"best f1 under pa": {"f1": 0.9819494584837539, "precision": 0.9645390070921985, "recall": 1.0, "threshold": 54.38000000000001}, "event-based f1 under pa with mode log": {"f1": 0.8888888888888884, "precision": 0.8, "recall": 1.0, "threshold": 54.38000000000001}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999994, "precision": 0.857142857142857, "recall": 0.7499999999999999, "threshold": 57.77}}, "123": {"best f1 under pa": {"f1": 0.31481481481481455, "precision": 0.18681318681318682, "recall": 1.0, "threshold": 7.375}, "event-based f1 under pa with mode log": {"f1": 0.051282051282051225, "precision": 0.02631578947368421, "recall": 0.9999999999999996, "threshold": 7.375}, "event-based f1 under pa with mode squeeze": {"f1": 0.026315789473684178, "precision": 0.013333333333333334, "recall": 0.9999999999999989, "threshold": 7.375}}, "137": {"best f1 under pa": {"f1": 0.923857868020304, "precision": 0.8778135048231511, "recall": 0.975, "threshold": 109.75}, "event-based f1 under pa with mode log": {"f1": 0.6923076923076917, "precision": 0.6923076923076923, "recall": 0.6923076923076923, "threshold": 112.8125}, "event-based f1 under pa with mode squeeze": {"f1": 0.5882352941176464, "precision": 0.9999999999999998, "recall": 0.41666666666666663, "threshold": 119.625}}, "133": {"best f1 under pa": {"f1": 0.989690721649484, "precision": 0.9795918367346939, "recall": 1.0, "threshold": 268.125}, "event-based f1 under pa with mode log": {"f1": 0.92063492063492, "precision": 0.8529411764705882, "recall": 1.0, "threshold": 268.125}, "event-based f1 under pa with mode squeeze": {"f1": 0.8387096774193542, "precision": 0.7222222222222222, "recall": 0.9999999999999999, "threshold": 268.125}}, "32": {"best f1 under pa": {"f1": 0.9957686882933704, "precision": 0.9915730337078652, "recall": 1.0, "threshold": 4.333333432675}, "event-based f1 under pa with mode log": {"f1": 0.9677419354838704, "precision": 0.9375, "recall": 1.0, "threshold": 4.333333432675}, "event-based f1 under pa with mode squeeze": {"f1": 0.9333333333333328, "precision": 0.875, "recall": 1.0, "threshold": 4.333333432675}}, "26": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 11147.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 11147.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 11147.0}}, "27": {"best f1 under pa": {"f1": 0.979487179487179, "precision": 0.9597989949748744, "recall": 1.0, "threshold": 6.560606002809999}, "event-based f1 under pa with mode log": {"f1": 0.8947368421052626, "precision": 0.8095238095238095, "recall": 1.0, "threshold": 6.560606002809999}, "event-based f1 under pa with mode squeeze": {"f1": 0.8108108108108103, "precision": 0.75, "recall": 0.8823529411764706, "threshold": 7.39393949509}}, "33": {"best f1 under pa": {"f1": 0.9460784313725484, "precision": 0.8976744186046511, "recall": 1.0, "threshold": 39.03}, "event-based f1 under pa with mode log": {"f1": 0.9032258064516123, "precision": 0.9999999999999999, "recall": 0.8235294117647058, "threshold": 55.13}, "event-based f1 under pa with mode squeeze": {"f1": 0.9090909090909084, "precision": 0.9999999999999998, "recall": 0.8333333333333333, "threshold": 55.13}}, "132": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 280.5}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 280.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 280.5}}, "118": {"best f1 under pa": {"f1": 0.9834710743801648, "precision": 0.967479674796748, "recall": 1.0, "threshold": 7778.0}, "event-based f1 under pa with mode log": {"f1": 0.8181818181818175, "precision": 0.6923076923076922, "recall": 0.9999999999999998, "threshold": 7778.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428564, "precision": 0.9999999999999997, "recall": 0.7499999999999999, "threshold": 8417.0}}, "124": {"best f1 under pa": {"f1": 0.9767441860465111, "precision": 0.9545454545454546, "recall": 1.0, "threshold": 10.3125}, "event-based f1 under pa with mode log": {"f1": 0.7692307692307686, "precision": 0.6249999999999999, "recall": 0.9999999999999998, "threshold": 10.3125}, "event-based f1 under pa with mode squeeze": {"f1": 0.5714285714285708, "precision": 0.3999999999999999, "recall": 0.9999999999999996, "threshold": 10.3125}}, "25": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 10277.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 10277.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 10277.0}}, "31": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 5.25000000004}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 5.25000000004}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 5.25000000004}}, "19": {"best f1 under pa": {"f1": 0.9803921568627445, "precision": 0.9615384615384616, "recall": 1.0, "threshold": 1987.0}, "event-based f1 under pa with mode log": {"f1": 0.7692307692307686, "precision": 0.6249999999999999, "recall": 0.9999999999999998, "threshold": 1987.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.5714285714285708, "precision": 0.3999999999999999, "recall": 0.9999999999999996, "threshold": 1987.0}}, "30": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 6.25}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 6.25}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 6.25}}, "131": {"best f1 under pa": {"f1": 0.7378640776699025, "precision": 0.926829268292683, "recall": 0.6129032258064516, "threshold": 32.4375}, "event-based f1 under pa with mode log": {"f1": 0.666666666666666, "precision": 0.6666666666666665, "recall": 0.6666666666666665, "threshold": 32.4375}, "event-based f1 under pa with mode squeeze": {"f1": 0.666666666666666, "precision": 0.9999999999999996, "recall": 0.4999999999999999, "threshold": 40.9375}}, "125": {"best f1 under pa": {"f1": 0.9082568807339443, "precision": 0.99, "recall": 0.8389830508474576, "threshold": 15.4375}, "event-based f1 under pa with mode log": {"f1": 0.8888888888888882, "precision": 0.9230769230769229, "recall": 0.857142857142857, "threshold": 15.4375}, "event-based f1 under pa with mode squeeze": {"f1": 0.8333333333333327, "precision": 0.8333333333333333, "recall": 0.8333333333333333, "threshold": 15.4375}}, "119": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 10855.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 10855.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 10855.0}}, "142": {"best f1 under pa": {"f1": 0.9767441860465111, "precision": 0.9545454545454546, "recall": 1.0, "threshold": 4.75}, "event-based f1 under pa with mode log": {"f1": 0.7499999999999992, "precision": 0.5999999999999999, "recall": 0.9999999999999997, "threshold": 4.75}, "event-based f1 under pa with mode squeeze": {"f1": 0.49999999999999933, "precision": 0.33333333333333326, "recall": 0.9999999999999989, "threshold": 4.75}}, "156": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3147.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3147.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 3147.0}}, "80": {"best f1 under pa": {"f1": 0.9952963311382872, "precision": 0.9906367041198502, "recall": 1.0, "threshold": 187.81}, "event-based f1 under pa with mode log": {"f1": 0.8936170212765951, "precision": 0.9130434782608695, "recall": 0.875, "threshold": 212.62}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999994, "precision": 0.7499999999999999, "recall": 0.857142857142857, "threshold": 212.62}}, "94": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3403.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3403.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 3403.0}}, "181": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3849.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3849.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 3849.0}}, "5": {"best f1 under pa": {"f1": 0.901234567901234, "precision": 0.8202247191011236, "recall": 1.0, "threshold": 28.8333358764}, "event-based f1 under pa with mode log": {"f1": 0.7428571428571423, "precision": 0.65, "recall": 0.8666666666666666, "threshold": 32.6666688918}, "event-based f1 under pa with mode squeeze": {"f1": 0.5714285714285707, "precision": 0.4999999999999999, "recall": 0.6666666666666665, "threshold": 34.0000009536}}, "57": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 5.971052289020001}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 5.971052289020001}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 5.971052289020001}}, "4": {"best f1 under pa": {"f1": 0.8684210526315784, "precision": 0.7674418604651163, "recall": 1.0, "threshold": 31.75}, "event-based f1 under pa with mode log": {"f1": 0.666666666666666, "precision": 0.9999999999999996, "recall": 0.4999999999999999, "threshold": 40.583333969099996}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666657, "precision": 0.9999999999999989, "recall": 0.4999999999999998, "threshold": 40.583333969099996}}, "194": {"best f1 under pa": {"f1": 0.9981785063752271, "precision": 0.9963636363636363, "recall": 1.0, "threshold": 6325.0}, "event-based f1 under pa with mode log": {"f1": 0.9333333333333327, "precision": 0.8749999999999998, "recall": 0.9999999999999999, "threshold": 6325.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999992, "precision": 0.6666666666666665, "recall": 0.9999999999999996, "threshold": 6325.0}}, "180": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2630.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2630.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2630.0}}, "95": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3658.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3658.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 3658.0}}, "157": {"best f1 under pa": {"f1": 0.9927007299270069, "precision": 0.9855072463768116, "recall": 1.0, "threshold": 3499.0}, "event-based f1 under pa with mode log": {"f1": 0.9523809523809518, "precision": 0.9090909090909091, "recall": 1.0, "threshold": 3499.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9473684210526309, "precision": 0.9999999999999998, "recall": 0.8999999999999998, "threshold": 4136.0}}, "143": {"best f1 under pa": {"f1": 0.9081632653061219, "precision": 0.8317757009345794, "recall": 1.0, "threshold": 30.6875}, "event-based f1 under pa with mode log": {"f1": 0.7058823529411757, "precision": 0.7499999999999999, "recall": 0.6666666666666665, "threshold": 32.0625}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666661, "precision": 0.5999999999999999, "recall": 0.7499999999999999, "threshold": 32.0625}}, "209": {"best f1 under pa": {"f1": 0.9228187919463081, "precision": 0.8566978193146417, "recall": 1.0, "threshold": 39.24}, "event-based f1 under pa with mode log": {"f1": 0.7857142857142851, "precision": 0.9166666666666665, "recall": 0.6875, "threshold": 52.88}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999993, "precision": 0.7999999999999998, "recall": 0.7999999999999998, "threshold": 52.88}}, "155": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2516.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2516.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2516.0}}, "97": {"best f1 under pa": {"f1": 0.8131868131868127, "precision": 0.6851851851851852, "recall": 1.0, "threshold": 10062.0}, "event-based f1 under pa with mode log": {"f1": 0.260869565217391, "precision": 0.15, "recall": 0.9999999999999997, "threshold": 10062.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.10526315789473671, "precision": 0.05555555555555555, "recall": 0.9999999999999989, "threshold": 10062.0}}, "169": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2949.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2949.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2949.0}}, "83": {"best f1 under pa": {"f1": 0.8793844308983706, "precision": 0.7847333690030319, "recall": 1.0, "threshold": 4113.0}, "event-based f1 under pa with mode log": {"f1": 0.011466011466011455, "precision": 0.005766062602965404, "recall": 0.9999999999999999, "threshold": 4113.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.001654259718775846, "precision": 0.0008278145695364238, "recall": 0.9999999999999989, "threshold": 4113.0}}, "68": {"best f1 under pa": {"f1": 0.9589041095890405, "precision": 0.9210526315789473, "recall": 1.0, "threshold": 8131.0}, "event-based f1 under pa with mode log": {"f1": 0.7272727272727267, "precision": 0.5714285714285714, "recall": 0.9999999999999998, "threshold": 8131.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666657, "precision": 0.9999999999999989, "recall": 0.4999999999999998, "threshold": 12322.0}}, "196": {"best f1 under pa": {"f1": 0.9743589743589738, "precision": 0.95, "recall": 1.0, "threshold": 15243.0}, "event-based f1 under pa with mode log": {"f1": 0.7999999999999994, "precision": 0.6666666666666665, "recall": 0.9999999999999998, "threshold": 15243.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.666666666666666, "precision": 0.4999999999999999, "recall": 0.9999999999999996, "threshold": 15243.0}}, "182": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4847.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 4847.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 4847.0}}, "54": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 31.95000123976}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 31.95000123976}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 31.95000123976}}, "183": {"best f1 under pa": {"f1": 0.9864253393665153, "precision": 0.9732142857142857, "recall": 1.0, "threshold": 4888.0}, "event-based f1 under pa with mode log": {"f1": 0.9032258064516123, "precision": 0.8235294117647058, "recall": 0.9999999999999999, "threshold": 4888.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.8235294117647052, "precision": 0.6999999999999998, "recall": 0.9999999999999999, "threshold": 4888.0}}, "69": {"best f1 under pa": {"f1": 0.9969418960244643, "precision": 0.9939024390243902, "recall": 1.0, "threshold": 50.260000000000005}, "event-based f1 under pa with mode log": {"f1": 0.9599999999999992, "precision": 0.9230769230769229, "recall": 0.9999999999999999, "threshold": 50.260000000000005}, "event-based f1 under pa with mode squeeze": {"f1": 0.8888888888888881, "precision": 0.7999999999999998, "recall": 0.9999999999999998, "threshold": 50.260000000000005}}, "197": {"best f1 under pa": {"f1": 0.5531914893617017, "precision": 0.38235294117647056, "recall": 1.0, "threshold": 9310.0}, "event-based f1 under pa with mode log": {"f1": 0.1599999999999998, "precision": 0.08695652173913043, "recall": 0.9999999999999999, "threshold": 9310.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.08695652173913034, "precision": 0.045454545454545456, "recall": 0.9999999999999997, "threshold": 9310.0}}, "96": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 8241.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 8241.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 8241.0}}, "168": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2471.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2471.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 2471.0}}, "140": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4.191666662693001}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 4.191666662693001}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 4.191666662693001}}, "154": {"best f1 under pa": {"f1": 0.9962264150943391, "precision": 0.9924812030075187, "recall": 1.0, "threshold": 2959.0}, "event-based f1 under pa with mode log": {"f1": 0.9705882352941171, "precision": 0.9428571428571428, "recall": 1.0, "threshold": 2959.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9411764705882347, "precision": 0.8888888888888888, "recall": 1.0, "threshold": 2959.0}}, "86": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 14482.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 14482.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 14482.0}}, "178": {"best f1 under pa": {"f1": 0.9113924050632906, "precision": 0.8372093023255814, "recall": 1.0, "threshold": 250.57}, "event-based f1 under pa with mode log": {"f1": 0.461538461538461, "precision": 0.29999999999999993, "recall": 0.9999999999999997, "threshold": 250.57}, "event-based f1 under pa with mode squeeze": {"f1": 0.22222222222222193, "precision": 0.12499999999999997, "recall": 0.9999999999999989, "threshold": 250.57}}, "150": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2541.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2541.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2541.0}}, "144": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4.3125}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4.3125}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4.3125}}, "45": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 5.997368097319997}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 5.997368097319997}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 5.997368097319997}}, "193": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 9458.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 9458.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 9458.0}}, "79": {"best f1 under pa": {"f1": 0.9047619047619042, "precision": 1.0, "recall": 0.8260869565217391, "threshold": 71.13}, "event-based f1 under pa with mode log": {"f1": 0.8888888888888884, "precision": 1.0, "recall": 0.8, "threshold": 71.13}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428564, "precision": 0.9999999999999999, "recall": 0.7499999999999999, "threshold": 71.13}}, "187": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 7620.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 7620.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 7620.0}}, "78": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 84.49000000000001}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 84.49000000000001}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 84.49000000000001}}, "186": {"best f1 under pa": {"f1": 0.991354466858789, "precision": 0.9828571428571429, "recall": 1.0, "threshold": 9324.5}, "event-based f1 under pa with mode log": {"f1": 0.8421052631578942, "precision": 0.7272727272727273, "recall": 1.0, "threshold": 9324.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.7272727272727265, "precision": 0.7999999999999998, "recall": 0.6666666666666665, "threshold": 11299.0}}, "192": {"best f1 under pa": {"f1": 0.9411764705882347, "precision": 0.8888888888888888, "recall": 1.0, "threshold": 9763.0}, "event-based f1 under pa with mode log": {"f1": 0.777777777777777, "precision": 0.7777777777777777, "recall": 0.7777777777777777, "threshold": 10968.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666661, "precision": 0.5999999999999999, "recall": 0.7499999999999999, "threshold": 10968.0}}, "50": {"best f1 under pa": {"f1": 0.9670886075949362, "precision": 0.9362745098039216, "recall": 1.0, "threshold": 13.26249933247}, "event-based f1 under pa with mode log": {"f1": 0.666666666666666, "precision": 0.8333333333333333, "recall": 0.5555555555555555, "threshold": 13.462499618559999}, "event-based f1 under pa with mode squeeze": {"f1": 0.666666666666666, "precision": 0.6666666666666665, "recall": 0.6666666666666665, "threshold": 13.462499618559999}}, "145": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 6.375000476869999}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 6.375000476869999}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 6.375000476869999}}, "151": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4127.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4127.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 4127.0}}, "179": {"best f1 under pa": {"f1": 0.9523809523809518, "precision": 0.9090909090909091, "recall": 1.0, "threshold": 193.25000000000003}, "event-based f1 under pa with mode log": {"f1": 0.7142857142857136, "precision": 0.5555555555555555, "recall": 0.9999999999999998, "threshold": 193.25000000000003}, "event-based f1 under pa with mode squeeze": {"f1": 0.49999999999999944, "precision": 0.33333333333333326, "recall": 0.9999999999999996, "threshold": 193.25000000000003}}, "93": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2403.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2403.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2403.0}}, "85": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 10927.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 10927.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 10927.0}}, "147": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 5.25}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 5.25}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 5.25}}, "153": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4098.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4098.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 4098.0}}, "46": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 17.40789461136}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 17.40789461136}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 17.40789461136}}, "184": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3043.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3043.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 3043.0}}, "190": {"best f1 under pa": {"f1": 0.99047619047619, "precision": 0.9811320754716981, "recall": 1.0, "threshold": 10376.0}, "event-based f1 under pa with mode log": {"f1": 0.9230769230769225, "precision": 0.857142857142857, "recall": 0.9999999999999999, "threshold": 10376.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428564, "precision": 0.7499999999999999, "recall": 0.9999999999999997, "threshold": 10376.0}}, "191": {"best f1 under pa": {"f1": 0.2622950819672127, "precision": 0.18045112781954886, "recall": 0.48, "threshold": 8852.0}, "event-based f1 under pa with mode log": {"f1": 0.06611570247933872, "precision": 0.035398230088495575, "recall": 0.4999999999999999, "threshold": 8852.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.034782608695652105, "precision": 0.018018018018018018, "recall": 0.4999999999999999, "threshold": 8852.0}}, "185": {"best f1 under pa": {"f1": 0.9398663697104671, "precision": 1.0, "recall": 0.8865546218487395, "threshold": 3655.0}, "event-based f1 under pa with mode log": {"f1": 0.928571428571428, "precision": 1.0, "recall": 0.8666666666666667, "threshold": 3655.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9285714285714279, "precision": 0.9999999999999999, "recall": 0.8666666666666666, "threshold": 3655.0}}, "53": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 12.10000061991}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 12.10000061991}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 12.10000061991}}, "47": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 16.461402893059997}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 16.461402893059997}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 16.461402893059997}}, "152": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2819.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2819.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2819.0}}, "146": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4.6875}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4.6875}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4.6875}}, "84": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 6240.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 6240.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 6240.0}}, "201": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 67.33}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 67.33}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 67.33}}, "163": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 297.76}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 297.76}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 297.76}}, "177": {"best f1 under pa": {"f1": 0.90566037735849, "precision": 0.8275862068965517, "recall": 1.0, "threshold": 251.17}, "event-based f1 under pa with mode log": {"f1": 0.580645161290322, "precision": 0.47368421052631576, "recall": 0.7499999999999999, "threshold": 261.89}, "event-based f1 under pa with mode squeeze": {"f1": 0.428571428571428, "precision": 0.33333333333333326, "recall": 0.5999999999999999, "threshold": 266.4}}, "89": {"best f1 under pa": {"f1": 0.038260869565217355, "precision": 0.01950354609929078, "recall": 1.0, "threshold": 2224.0}, "event-based f1 under pa with mode log": {"f1": 0.0036036036036035993, "precision": 0.0018050541516245488, "recall": 0.9999999999999996, "threshold": 2224.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.0018050541516245466, "precision": 0.0009033423667570009, "recall": 0.9999999999999989, "threshold": 2224.0}}, "62": {"best f1 under pa": {"f1": 0.9969604863221879, "precision": 0.9939393939393939, "recall": 1.0, "threshold": 2.2999999523100003}, "event-based f1 under pa with mode log": {"f1": 0.8888888888888881, "precision": 0.7999999999999998, "recall": 0.9999999999999998, "threshold": 2.2999999523100003}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666657, "precision": 0.4999999999999998, "recall": 0.9999999999999989, "threshold": 2.2999999523100003}}, "188": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 7455.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 7455.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 7455.0}}, "176": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 404.33000000000004}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 404.33000000000004}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 404.33000000000004}}, "200": {"best f1 under pa": {"f1": 0.9062499999999994, "precision": 0.8285714285714286, "recall": 1.0, "threshold": 47.919999999999995}, "event-based f1 under pa with mode log": {"f1": 0.49999999999999944, "precision": 0.33333333333333326, "recall": 0.9999999999999997, "threshold": 47.919999999999995}, "event-based f1 under pa with mode squeeze": {"f1": 0.2499999999999997, "precision": 0.14285714285714285, "recall": 0.9999999999999989, "threshold": 47.919999999999995}}, "202": {"best f1 under pa": {"f1": 0.9615384615384609, "precision": 0.9259259259259259, "recall": 1.0, "threshold": 34.85}, "event-based f1 under pa with mode log": {"f1": 0.8749999999999992, "precision": 0.9999999999999999, "recall": 0.7777777777777777, "threshold": 36.97}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428564, "precision": 0.9999999999999997, "recall": 0.7499999999999999, "threshold": 36.97}}, "174": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 445.6}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 445.6}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 445.6}}, "160": {"best f1 under pa": {"f1": 0.9790209790209785, "precision": 0.958904109589041, "recall": 1.0, "threshold": 2586.0}, "event-based f1 under pa with mode log": {"f1": 0.9302325581395344, "precision": 1.0, "recall": 0.8695652173913043, "threshold": 3055.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9523809523809517, "precision": 0.9999999999999998, "recall": 0.909090909090909, "threshold": 3055.0}}, "148": {"best f1 under pa": {"f1": 0.9939148073022307, "precision": 0.9879032258064516, "recall": 1.0, "threshold": 5.375}, "event-based f1 under pa with mode log": {"f1": 0.9620253164556958, "precision": 0.926829268292683, "recall": 1.0, "threshold": 5.375}, "event-based f1 under pa with mode squeeze": {"f1": 0.9230769230769225, "precision": 0.8571428571428571, "recall": 1.0, "threshold": 5.375}}, "49": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2.9500002860900003}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 2.9500002860900003}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 2.9500002860900003}}, "61": {"best f1 under pa": {"f1": 0.985074626865671, "precision": 0.9705882352941176, "recall": 1.0, "threshold": 1.8500001430499997}, "event-based f1 under pa with mode log": {"f1": 0.7499999999999992, "precision": 0.5999999999999999, "recall": 0.9999999999999997, "threshold": 1.8500001430499997}, "event-based f1 under pa with mode squeeze": {"f1": 0.49999999999999933, "precision": 0.33333333333333326, "recall": 0.9999999999999989, "threshold": 1.8500001430499997}}, "60": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 45.368421793050004}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 45.368421793050004}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 45.368421793050004}}, "74": {"best f1 under pa": {"f1": 0.9641577060931894, "precision": 0.9307958477508651, "recall": 1.0, "threshold": 45.650000000000006}, "event-based f1 under pa with mode log": {"f1": 0.8235294117647053, "precision": 0.8235294117647058, "recall": 0.8235294117647058, "threshold": 49.49}, "event-based f1 under pa with mode squeeze": {"f1": 0.7272727272727265, "precision": 0.7999999999999998, "recall": 0.6666666666666665, "threshold": 54.24}}, "48": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 47.6499981881}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 47.6499981881}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 47.6499981881}}, "149": {"best f1 under pa": {"f1": 0.9944598337950133, "precision": 0.9986091794158554, "recall": 0.9903448275862069, "threshold": 4.237500190730001}, "event-based f1 under pa with mode log": {"f1": 0.9756097560975604, "precision": 0.9836065573770492, "recall": 0.967741935483871, "threshold": 4.237500190730001}, "event-based f1 under pa with mode squeeze": {"f1": 0.9642857142857137, "precision": 0.9642857142857143, "recall": 0.9642857142857143, "threshold": 4.237500190730001}}, "161": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3681.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3681.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 3681.0}}, "203": {"best f1 under pa": {"f1": 0.9140369967355816, "precision": 0.8416833667334669, "recall": 1.0, "threshold": 29.83}, "event-based f1 under pa with mode log": {"f1": 0.8421052631578941, "precision": 0.9411764705882353, "recall": 0.7619047619047619, "threshold": 33.8}, "event-based f1 under pa with mode squeeze": {"f1": 0.8749999999999993, "precision": 0.8749999999999998, "recall": 0.8749999999999998, "threshold": 33.8}}, "207": {"best f1 under pa": {"f1": 0.9891304347826082, "precision": 0.978494623655914, "recall": 1.0, "threshold": 73.19999999999999}, "event-based f1 under pa with mode log": {"f1": 0.9411764705882347, "precision": 0.8888888888888888, "recall": 1.0, "threshold": 73.19999999999999}, "event-based f1 under pa with mode squeeze": {"f1": 0.8749999999999992, "precision": 0.7777777777777777, "recall": 0.9999999999999999, "threshold": 73.19999999999999}}, "159": {"best f1 under pa": {"f1": 0.9970845481049557, "precision": 0.9941860465116279, "recall": 1.0, "threshold": 3472.0}, "event-based f1 under pa with mode log": {"f1": 0.9803921568627445, "precision": 0.9615384615384616, "recall": 1.0, "threshold": 3472.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9599999999999992, "precision": 0.9230769230769229, "recall": 0.9999999999999999, "threshold": 3472.0}}, "171": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4801.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4801.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 4801.0}}, "165": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 359.3}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 359.3}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 359.3}}, "70": {"best f1 under pa": {"f1": 0.9949494949494944, "precision": 0.9899497487437185, "recall": 1.0, "threshold": 49.870000000000005}, "event-based f1 under pa with mode log": {"f1": 0.9230769230769225, "precision": 0.857142857142857, "recall": 0.9999999999999999, "threshold": 49.870000000000005}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999994, "precision": 0.6666666666666665, "recall": 0.9999999999999998, "threshold": 49.870000000000005}}, "58": {"best f1 under pa": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 5.113157749179999}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 5.113157749179999}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 5.113157749179999}}, "59": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 15.877777099649999}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 15.877777099649999}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 15.877777099649999}}, "65": {"best f1 under pa": {"f1": 0.9930069930069925, "precision": 0.9861111111111112, "recall": 1.0, "threshold": 9049.0}, "event-based f1 under pa with mode log": {"f1": 0.7272727272727267, "precision": 0.5714285714285714, "recall": 0.9999999999999998, "threshold": 9049.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.39999999999999947, "precision": 0.24999999999999994, "recall": 0.9999999999999989, "threshold": 9049.0}}, "71": {"best f1 under pa": {"f1": 0.9172576832151295, "precision": 0.9897959183673469, "recall": 0.8546255506607929, "threshold": 85.53999999999999}, "event-based f1 under pa with mode log": {"f1": 0.8852459016393438, "precision": 0.8709677419354839, "recall": 0.9, "threshold": 85.53999999999999}, "event-based f1 under pa with mode squeeze": {"f1": 0.7826086956521733, "precision": 0.6923076923076922, "recall": 0.8999999999999998, "threshold": 85.53999999999999}}, "164": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 404.76}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 404.76}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 404.76}}, "170": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 5741.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 5741.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 5741.0}}, "158": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4195.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4195.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 4195.0}}, "204": {"best f1 under pa": {"f1": 0.9900990099009894, "precision": 0.9803921568627451, "recall": 1.0, "threshold": 48.720000000000006}, "event-based f1 under pa with mode log": {"f1": 0.9090909090909084, "precision": 0.8333333333333333, "recall": 0.9999999999999998, "threshold": 48.720000000000006}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999992, "precision": 0.6666666666666665, "recall": 0.9999999999999996, "threshold": 48.720000000000006}}, "166": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 284.07}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 284.07}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 284.07}}, "98": {"best f1 under pa": {"f1": 0.9824561403508766, "precision": 0.9655172413793104, "recall": 1.0, "threshold": 7191.0}, "event-based f1 under pa with mode log": {"f1": 0.8571428571428564, "precision": 0.7499999999999999, "recall": 0.9999999999999997, "threshold": 7191.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.6666666666666657, "precision": 0.4999999999999998, "recall": 0.9999999999999989, "threshold": 7191.0}}, "172": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3239.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3239.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 3239.0}}, "199": {"best f1 under pa": {"f1": 0.98976109215017, "precision": 0.9797297297297297, "recall": 1.0, "threshold": 74.62}, "event-based f1 under pa with mode log": {"f1": 0.8965517241379304, "precision": 0.8125, "recall": 0.9999999999999999, "threshold": 74.62}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999993, "precision": 0.7999999999999998, "recall": 0.7999999999999998, "threshold": 78.97}}, "67": {"best f1 under pa": {"f1": 0.9795918367346933, "precision": 0.96, "recall": 1.0, "threshold": 7908.5}, "event-based f1 under pa with mode log": {"f1": 0.8571428571428565, "precision": 0.7499999999999999, "recall": 0.9999999999999998, "threshold": 7908.5}, "event-based f1 under pa with mode squeeze": {"f1": 0.7499999999999994, "precision": 0.7499999999999999, "recall": 0.7499999999999999, "threshold": 8708.0}}, "73": {"best f1 under pa": {"f1": 0.9962406015037588, "precision": 0.9925093632958801, "recall": 1.0, "threshold": 171.39}, "event-based f1 under pa with mode log": {"f1": 0.9166666666666661, "precision": 0.846153846153846, "recall": 0.9999999999999999, "threshold": 171.39}, "event-based f1 under pa with mode squeeze": {"f1": 0.7499999999999992, "precision": 0.5999999999999999, "recall": 0.9999999999999997, "threshold": 171.39}}, "9": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 5948.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 5948.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 5948.0}}, "72": {"best f1 under pa": {"f1": 0.9614512471655324, "precision": 0.925764192139738, "recall": 1.0, "threshold": 46.84}, "event-based f1 under pa with mode log": {"f1": 0.8695652173913037, "precision": 0.9999999999999998, "recall": 0.7692307692307692, "threshold": 65.89500000000001}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428564, "precision": 0.9999999999999997, "recall": 0.7499999999999999, "threshold": 65.89500000000001}}, "198": {"best f1 under pa": {"f1": 0.99047619047619, "precision": 0.9811320754716981, "recall": 1.0, "threshold": 44.8}, "event-based f1 under pa with mode log": {"f1": 0.9090909090909084, "precision": 0.8333333333333333, "recall": 0.9999999999999998, "threshold": 44.8}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999992, "precision": 0.6666666666666665, "recall": 0.9999999999999996, "threshold": 44.8}}, "66": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 12190.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 12190.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 12190.0}}, "173": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3206.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3206.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 3206.0}}, "167": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 195.52}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 195.52}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 195.52}}, "99": {"best f1 under pa": {"f1": 0.9923664122137399, "precision": 0.9848484848484849, "recall": 1.0, "threshold": 8521.0}, "event-based f1 under pa with mode log": {"f1": 0.9230769230769225, "precision": 0.857142857142857, "recall": 0.9999999999999999, "threshold": 8521.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.7999999999999992, "precision": 0.6666666666666665, "recall": 0.9999999999999996, "threshold": 8521.0}}, "205": {"best f1 under pa": {"f1": 0.9722222222222217, "precision": 0.9459459459459459, "recall": 1.0, "threshold": 93.79333333333335}, "event-based f1 under pa with mode log": {"f1": 0.7499999999999992, "precision": 0.5999999999999999, "recall": 0.9999999999999997, "threshold": 93.79333333333335}, "event-based f1 under pa with mode squeeze": {"f1": 0.49999999999999933, "precision": 0.33333333333333326, "recall": 0.9999999999999989, "threshold": 93.79333333333335}}, "128": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 5.920833587600001}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 5.920833587600001}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 5.920833587600001}}, "29": {"best f1 under pa": {"f1": 0.9912536443148682, "precision": 0.9826589595375722, "recall": 1.0, "threshold": 7.416667103810001}, "event-based f1 under pa with mode log": {"f1": 0.9189189189189183, "precision": 0.85, "recall": 1.0, "threshold": 7.416667103810001}, "event-based f1 under pa with mode squeeze": {"f1": 0.8421052631578942, "precision": 0.7272727272727273, "recall": 1.0, "threshold": 7.416667103810001}}, "15": {"best f1 under pa": {"f1": 0.931343283582089, "precision": 0.8715083798882681, "recall": 1.0, "threshold": 49.26}, "event-based f1 under pa with mode log": {"f1": 0.6896551724137926, "precision": 0.8333333333333333, "recall": 0.5882352941176471, "threshold": 56.480000000000004}, "event-based f1 under pa with mode squeeze": {"f1": 0.5999999999999993, "precision": 0.5999999999999999, "recall": 0.5999999999999999, "threshold": 56.480000000000004}}, "14": {"best f1 under pa": {"f1": 0.986666666666666, "precision": 0.9736842105263158, "recall": 1.0, "threshold": 52.61}, "event-based f1 under pa with mode log": {"f1": 0.9189189189189183, "precision": 0.85, "recall": 1.0, "threshold": 52.61}, "event-based f1 under pa with mode squeeze": {"f1": 0.8749999999999993, "precision": 0.8749999999999998, "recall": 0.8749999999999998, "threshold": 55.86999999999999}}, "28": {"best f1 under pa": {"f1": 0.9953917050691239, "precision": 0.9908256880733946, "recall": 1.0, "threshold": 13.09999954704}, "event-based f1 under pa with mode log": {"f1": 0.9743589743589738, "precision": 0.95, "recall": 1.0, "threshold": 13.09999954704}, "event-based f1 under pa with mode squeeze": {"f1": 0.9499999999999995, "precision": 0.9047619047619048, "recall": 1.0, "threshold": 13.09999954704}}, "101": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 11347.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999997, "recall": 0.9999999999999997, "threshold": 11347.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 11347.0}}, "117": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 20766.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999991, "precision": 0.9999999999999996, "recall": 0.9999999999999996, "threshold": 20766.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999983, "precision": 0.9999999999999989, "recall": 0.9999999999999989, "threshold": 20766.0}}, "103": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4968.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 4968.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 4968.0}}, "16": {"best f1 under pa": {"f1": 0.991379310344827, "precision": 0.9829059829059829, "recall": 1.0, "threshold": 57.2}, "event-based f1 under pa with mode log": {"f1": 0.8888888888888881, "precision": 0.7999999999999998, "recall": 0.9999999999999998, "threshold": 57.2}, "event-based f1 under pa with mode squeeze": {"f1": 0.7499999999999992, "precision": 0.5999999999999999, "recall": 0.9999999999999997, "threshold": 57.2}}, "17": {"best f1 under pa": {"f1": 0.9925187032418947, "precision": 0.9851485148514851, "recall": 1.0, "threshold": 43.16}, "event-based f1 under pa with mode log": {"f1": 0.8888888888888883, "precision": 0.7999999999999999, "recall": 0.9999999999999999, "threshold": 43.16}, "event-based f1 under pa with mode squeeze": {"f1": 0.8571428571428564, "precision": 0.9999999999999997, "recall": 0.7499999999999999, "threshold": 51.97}}, "102": {"best f1 under pa": {"f1": 0.9895833333333327, "precision": 0.979381443298969, "recall": 1.0, "threshold": 3752.0}, "event-based f1 under pa with mode log": {"f1": 0.9333333333333327, "precision": 0.875, "recall": 0.9999999999999999, "threshold": 3752.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.8749999999999992, "precision": 0.7777777777777777, "recall": 0.9999999999999999, "threshold": 3752.0}}, "116": {"best f1 under pa": {"f1": 0.869435315207648, "precision": 0.7690274841437632, "recall": 1.0, "threshold": 6790.0}, "event-based f1 under pa with mode log": {"f1": 0.010566037735849045, "precision": 0.005311077389984826, "recall": 0.9999999999999999, "threshold": 6790.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.0015232292460015215, "precision": 0.0007621951219512195, "recall": 0.9999999999999989, "threshold": 6790.0}}, "112": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2398.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2398.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2398.0}}, "106": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3080.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3080.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 3080.0}}, "13": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 55.71000000000001}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 55.71000000000001}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 55.71000000000001}}, "12": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 44.95}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 44.95}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 44.95}}, "107": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2813.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2813.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2813.0}}, "113": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3399.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3399.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 3399.0}}, "105": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2737.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2737.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2737.0}}, "111": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3655.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 3655.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999998, "recall": 0.9999999999999998, "threshold": 3655.0}}, "10": {"best f1 under pa": {"f1": 0.8176795580110492, "precision": 0.6915887850467289, "recall": 1.0, "threshold": 1728.0}, "event-based f1 under pa with mode log": {"f1": 0.15384615384615366, "precision": 0.08333333333333333, "recall": 0.9999999999999997, "threshold": 1728.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.057142857142857086, "precision": 0.029411764705882353, "recall": 0.9999999999999989, "threshold": 1728.0}}, "38": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 65.04}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 65.04}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 65.04}}, "110": {"best f1 under pa": {"f1": 0.9878048780487799, "precision": 0.9759036144578314, "recall": 1.0, "threshold": 2079.0}, "event-based f1 under pa with mode log": {"f1": 0.9090909090909084, "precision": 0.8333333333333333, "recall": 0.9999999999999998, "threshold": 2079.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.8888888888888881, "precision": 0.9999999999999998, "recall": 0.7999999999999998, "threshold": 2815.0}}, "104": {"best f1 under pa": {"f1": 0.9999999999999996, "precision": 1.0, "recall": 1.0, "threshold": 2357.0}, "event-based f1 under pa with mode log": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2357.0}, "event-based f1 under pa with mode squeeze": {"f1": 0.9999999999999993, "precision": 0.9999999999999999, "recall": 0.9999999999999999, "threshold": 2357.0}}}