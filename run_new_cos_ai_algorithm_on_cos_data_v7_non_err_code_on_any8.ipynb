{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b936432b", "metadata": {}, "outputs": [], "source": ["from typing import Dict\n", "import numpy as np\n", "import pandas as pd\n", "import sys\n", "from joblib import Parallel, delayed\n", "\n", "# Import cos_ai_service modules - use exact imports from the original code\n", "from cos_ai_algorithm_interfaces import CosAIServiceAlgo, calculate_micro_f1_score"]}, {"cell_type": "code", "execution_count": 2, "id": "b2d8e965", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "data_dir = './datasets/COS_normal_error_code/'\n", "data_dir = Path(data_dir)\n", "\n", "files = [f for f in data_dir.iterdir() if f.is_file()]\n", "files.sort(key=lambda x: int(x.name.split('.')[0]) if x.name.split('.')[0].isdigit() else float('inf'))\n", "\n", "def flag_calculation(file, params):\n", "    cur = pd.read_csv(file)\n", "    alg_instance = CosAIServiceAlgo(params=params)\n", "\n", "    test_len = cur['change_status'].sum()\n", "    alg_instance.test_phase_serialize(cur, test_len=test_len, is_filter=True)\n", "\n", "    cur['now_prediction'] = alg_instance.anomaly_flag()\n", "\n", "    cur['now_score'] = alg_instance.anomaly_score()\n", "\n", "    cur = cur[cur['change_status']==1]\n", "\n", "    return cur['now_prediction'].values, cur['now_score'].values, cur['prior_prediction'].values, cur['is_anomaly'].values\n", "\n", "params = {\n", "    \"param_list\": {'param_dtw_low': 0.088, 'param_dtw_high': 0.237, 'param_percent': 99.2, 'time_window_focus': 5, 'time_window_dtw': 4320, 'param_dtw': 0.13}\n", "}\n", "\n", "result_list = Parallel(n_jobs=32, backend='multiprocessing')(delayed(flag_calculation)(file, params) for file in files)\n", "\n", "now_preds_list = []\n", "now_scores_list = []\n", "prior_preds_list = []\n", "labels_list = []\n", "for result in result_list:\n", "    now_preds_list.append(result[0])\n", "    now_scores_list.append(result[1])\n", "    prior_preds_list.append(result[2])\n", "    labels_list.append(result[3])"]}, {"cell_type": "code", "execution_count": 3, "id": "647352b6", "metadata": {}, "outputs": [], "source": ["raw_report = calculate_micro_f1_score(now_preds_list, labels_list, mode='raw')"]}, {"cell_type": "code", "execution_count": 4, "id": "dda6c651", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'F1-score': 0.8598783285130143,\n", " 'Precision': 0.8261786124952089,\n", " 'Recall': 0.8964441671865253,\n", " 'total_tp': 4311,\n", " 'total_fp': 907,\n", " 'total_anomaly': 4809}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["raw_report"]}, {"cell_type": "code", "execution_count": 6, "id": "1a912141", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing file: datasets/COS_normal_error_code/8.csv\n", "Processing file: datasets/COS_normal_error_code/9.csv\n", "Processing file: datasets/COS_normal_error_code/10.csv\n", "Processing file: datasets/COS_normal_error_code/11.csv\n", "Processing file: datasets/COS_normal_error_code/12.csv\n", "Processing file: datasets/COS_normal_error_code/13.csv\n", "Processing file: datasets/COS_normal_error_code/14.csv\n", "Processing file: datasets/COS_normal_error_code/15.csv\n", "Processing file: datasets/COS_normal_error_code/16.csv\n", "Processing file: datasets/COS_normal_error_code/17.csv\n", "Processing file: datasets/COS_normal_error_code/18.csv\n", "Processing file: datasets/COS_normal_error_code/19.csv\n", "Processing file: datasets/COS_normal_error_code/20.csv\n", "Processing file: datasets/COS_normal_error_code/21.csv\n", "Processing file: datasets/COS_normal_error_code/22.csv\n", "Processing file: datasets/COS_normal_error_code/23.csv\n", "Processing file: datasets/COS_normal_error_code/24.csv\n", "Processing file: datasets/COS_normal_error_code/25.csv\n", "Processing file: datasets/COS_normal_error_code/26.csv\n", "Processing file: datasets/COS_normal_error_code/27.csv\n", "Processing file: datasets/COS_normal_error_code/28.csv\n", "Processing file: datasets/COS_normal_error_code/29.csv\n", "Processing file: datasets/COS_normal_error_code/30.csv\n", "Processing file: datasets/COS_normal_error_code/31.csv\n", "Processing file: datasets/COS_normal_error_code/32.csv\n", "Processing file: datasets/COS_normal_error_code/33.csv\n", "Processing file: datasets/COS_normal_error_code/34.csv\n", "Processing file: datasets/COS_normal_error_code/35.csv\n", "Processing file: datasets/COS_normal_error_code/36.csv\n", "Processing file: datasets/COS_normal_error_code/37.csv\n", "Processing file: datasets/COS_normal_error_code/38.csv\n", "Processing file: datasets/COS_normal_error_code/39.csv\n", "Processing file: datasets/COS_normal_error_code/40.csv\n", "Processing file: datasets/COS_normal_error_code/41.csv\n", "Processing file: datasets/COS_normal_error_code/42.csv\n", "Processing file: datasets/COS_normal_error_code/43.csv\n", "Processing file: datasets/COS_normal_error_code/44.csv\n", "Processing file: datasets/COS_normal_error_code/45.csv\n", "Processing file: datasets/COS_normal_error_code/46.csv\n", "Processing file: datasets/COS_normal_error_code/47.csv\n", "Processing file: datasets/COS_normal_error_code/48.csv\n", "Processing file: datasets/COS_normal_error_code/49.csv\n", "Processing file: datasets/COS_normal_error_code/50.csv\n", "Processing file: datasets/COS_normal_error_code/51.csv\n", "Processing file: datasets/COS_normal_error_code/52.csv\n", "Processing file: datasets/COS_normal_error_code/53.csv\n", "Processing file: datasets/COS_normal_error_code/54.csv\n", "Processing file: datasets/COS_normal_error_code/55.csv\n", "Processing file: datasets/COS_normal_error_code/56.csv\n", "Processing file: datasets/COS_normal_error_code/57.csv\n", "Processing file: datasets/COS_normal_error_code/58.csv\n", "Processing file: datasets/COS_normal_error_code/59.csv\n", "Processing file: datasets/COS_normal_error_code/60.csv\n", "Processing file: datasets/COS_normal_error_code/61.csv\n", "Processing file: datasets/COS_normal_error_code/62.csv\n", "Processing file: datasets/COS_normal_error_code/63.csv\n", "Processing file: datasets/COS_normal_error_code/64.csv\n", "Processing file: datasets/COS_normal_error_code/65.csv\n", "Processing file: datasets/COS_normal_error_code/66.csv\n", "Processing file: datasets/COS_normal_error_code/67.csv\n", "Processing file: datasets/COS_normal_error_code/68.csv\n", "Processing file: datasets/COS_normal_error_code/69.csv\n", "Processing file: datasets/COS_normal_error_code/70.csv\n", "Processing file: datasets/COS_normal_error_code/71.csv\n", "Processing file: datasets/COS_normal_error_code/72.csv\n", "Processing file: datasets/COS_normal_error_code/73.csv\n", "Processing file: datasets/COS_normal_error_code/74.csv\n", "Processing file: datasets/COS_normal_error_code/75.csv\n", "Processing file: datasets/COS_normal_error_code/76.csv\n", "Processing file: datasets/COS_normal_error_code/77.csv\n", "Processing file: datasets/COS_normal_error_code/78.csv\n", "Processing file: datasets/COS_normal_error_code/79.csv\n", "Processing file: datasets/COS_normal_error_code/80.csv\n", "Processing file: datasets/COS_normal_error_code/81.csv\n", "Processing file: datasets/COS_normal_error_code/82.csv\n", "Processing file: datasets/COS_normal_error_code/83.csv\n", "Processing file: datasets/COS_normal_error_code/84.csv\n", "Processing file: datasets/COS_normal_error_code/85.csv\n", "Processing file: datasets/COS_normal_error_code/86.csv\n", "Processing file: datasets/COS_normal_error_code/87.csv\n", "Processing file: datasets/COS_normal_error_code/88.csv\n", "Processing file: datasets/COS_normal_error_code/89.csv\n", "Processing file: datasets/COS_normal_error_code/90.csv\n", "Processing file: datasets/COS_normal_error_code/91.csv\n", "Processing file: datasets/COS_normal_error_code/92.csv\n", "Processing file: datasets/COS_normal_error_code/93.csv\n", "Processing file: datasets/COS_normal_error_code/94.csv\n", "Processing file: datasets/COS_normal_error_code/95.csv\n", "Processing file: datasets/COS_normal_error_code/96.csv\n", "Processing file: datasets/COS_normal_error_code/97.csv\n", "Processing file: datasets/COS_normal_error_code/98.csv\n", "Processing file: datasets/COS_normal_error_code/99.csv\n", "Processing file: datasets/COS_normal_error_code/100.csv\n", "Processing file: datasets/COS_normal_error_code/101.csv\n", "Processing file: datasets/COS_normal_error_code/102.csv\n", "Processing file: datasets/COS_normal_error_code/103.csv\n", "Processing file: datasets/COS_normal_error_code/104.csv\n", "Processing file: datasets/COS_normal_error_code/105.csv\n", "Processing file: datasets/COS_normal_error_code/106.csv\n", "Processing file: datasets/COS_normal_error_code/107.csv\n", "Processing file: datasets/COS_normal_error_code/108.csv\n", "Processing file: datasets/COS_normal_error_code/109.csv\n", "Processing file: datasets/COS_normal_error_code/110.csv\n", "Processing file: datasets/COS_normal_error_code/111.csv\n", "Processing file: datasets/COS_normal_error_code/112.csv\n", "Processing file: datasets/COS_normal_error_code/113.csv\n", "Processing file: datasets/COS_normal_error_code/114.csv\n", "Processing file: datasets/COS_normal_error_code/115.csv\n", "Processing file: datasets/COS_normal_error_code/116.csv\n", "Processing file: datasets/COS_normal_error_code/117.csv\n", "Processing file: datasets/COS_normal_error_code/118.csv\n", "Processing file: datasets/COS_normal_error_code/119.csv\n", "Processing file: datasets/COS_normal_error_code/120.csv\n", "Processing file: datasets/COS_normal_error_code/121.csv\n", "Processing file: datasets/COS_normal_error_code/122.csv\n", "Processing file: datasets/COS_normal_error_code/123.csv\n", "Processing file: datasets/COS_normal_error_code/124.csv\n", "Processing file: datasets/COS_normal_error_code/125.csv\n", "Processing file: datasets/COS_normal_error_code/126.csv\n", "Processing file: datasets/COS_normal_error_code/127.csv\n", "Processing file: datasets/COS_normal_error_code/128.csv\n", "Processing file: datasets/COS_normal_error_code/129.csv\n", "Processing file: datasets/COS_normal_error_code/130.csv\n", "Processing file: datasets/COS_normal_error_code/131.csv\n", "Processing file: datasets/COS_normal_error_code/132.csv\n", "Processing file: datasets/COS_normal_error_code/133.csv\n", "Processing file: datasets/COS_normal_error_code/134.csv\n", "Processing file: datasets/COS_normal_error_code/135.csv\n", "Processing file: datasets/COS_normal_error_code/136.csv\n", "Processing file: datasets/COS_normal_error_code/137.csv\n", "Processing file: datasets/COS_normal_error_code/138.csv\n", "Processing file: datasets/COS_normal_error_code/139.csv\n", "Processing file: datasets/COS_normal_error_code/140.csv\n", "Processing file: datasets/COS_normal_error_code/141.csv\n", "Processing file: datasets/COS_normal_error_code/142.csv\n", "Processing file: datasets/COS_normal_error_code/143.csv\n", "Processing file: datasets/COS_normal_error_code/144.csv\n", "Processing file: datasets/COS_normal_error_code/145.csv\n", "Processing file: datasets/COS_normal_error_code/146.csv\n", "Processing file: datasets/COS_normal_error_code/147.csv\n", "Processing file: datasets/COS_normal_error_code/148.csv\n", "Processing file: datasets/COS_normal_error_code/149.csv\n", "Processing file: datasets/COS_normal_error_code/150.csv\n", "Processing file: datasets/COS_normal_error_code/151.csv\n", "Processing file: datasets/COS_normal_error_code/152.csv\n", "Processing file: datasets/COS_normal_error_code/153.csv\n", "Processing file: datasets/COS_normal_error_code/154.csv\n", "Processing file: datasets/COS_normal_error_code/155.csv\n", "Processing file: datasets/COS_normal_error_code/156.csv\n", "Processing file: datasets/COS_normal_error_code/157.csv\n", "Processing file: datasets/COS_normal_error_code/158.csv\n", "Processing file: datasets/COS_normal_error_code/159.csv\n", "Processing file: datasets/COS_normal_error_code/160.csv\n", "Processing file: datasets/COS_normal_error_code/161.csv\n", "Processing file: datasets/COS_normal_error_code/162.csv\n", "Processing file: datasets/COS_normal_error_code/163.csv\n", "Processing file: datasets/COS_normal_error_code/164.csv\n", "Processing file: datasets/COS_normal_error_code/165.csv\n", "Processing file: datasets/COS_normal_error_code/166.csv\n", "Processing file: datasets/COS_normal_error_code/167.csv\n", "Processing file: datasets/COS_normal_error_code/168.csv\n", "Processing file: datasets/COS_normal_error_code/169.csv\n", "Processing file: datasets/COS_normal_error_code/170.csv\n", "Processing file: datasets/COS_normal_error_code/171.csv\n", "Processing file: datasets/COS_normal_error_code/172.csv\n", "Processing file: datasets/COS_normal_error_code/173.csv\n", "Processing file: datasets/COS_normal_error_code/174.csv\n", "Processing file: datasets/COS_normal_error_code/175.csv\n", "Processing file: datasets/COS_normal_error_code/176.csv\n", "Processing file: datasets/COS_normal_error_code/177.csv\n", "Processing file: datasets/COS_normal_error_code/178.csv\n", "Processing file: datasets/COS_normal_error_code/179.csv\n", "Processing file: datasets/COS_normal_error_code/180.csv\n", "Processing file: datasets/COS_normal_error_code/181.csv\n", "Processing file: datasets/COS_normal_error_code/182.csv\n", "Processing file: datasets/COS_normal_error_code/183.csv\n", "Processing file: datasets/COS_normal_error_code/184.csv\n", "Processing file: datasets/COS_normal_error_code/185.csv\n", "Processing file: datasets/COS_normal_error_code/186.csv\n", "Processing file: datasets/COS_normal_error_code/187.csv\n", "Processing file: datasets/COS_normal_error_code/188.csv\n", "Processing file: datasets/COS_normal_error_code/189.csv\n", "Processing file: datasets/COS_normal_error_code/190.csv\n", "Processing file: datasets/COS_normal_error_code/191.csv\n", "Processing file: datasets/COS_normal_error_code/192.csv\n", "Processing file: datasets/COS_normal_error_code/193.csv\n", "Processing file: datasets/COS_normal_error_code/194.csv\n", "Processing file: datasets/COS_normal_error_code/195.csv\n", "Processing file: datasets/COS_normal_error_code/196.csv\n", "Processing file: datasets/COS_normal_error_code/197.csv\n", "Processing file: datasets/COS_normal_error_code/198.csv\n", "Processing file: datasets/COS_normal_error_code/199.csv\n", "Processing file: datasets/COS_normal_error_code/200.csv\n", "Processing file: datasets/COS_normal_error_code/201.csv\n", "Processing file: datasets/COS_normal_error_code/202.csv\n", "Processing file: datasets/COS_normal_error_code/203.csv\n", "Processing file: datasets/COS_normal_error_code/204.csv\n", "Processing file: datasets/COS_normal_error_code/205.csv\n", "Processing file: datasets/COS_normal_error_code/206.csv\n", "Processing file: datasets/COS_normal_error_code/207.csv\n", "Processing file: datasets/COS_normal_error_code/208.csv\n", "Processing file: datasets/COS_normal_error_code/209.csv\n", "Processing file: datasets/COS_normal_error_code/210.csv\n", "Processing file: datasets/COS_normal_error_code/211.csv\n", "Processing file: datasets/COS_normal_error_code/212.csv\n", "Processing file: datasets/COS_normal_error_code/213.csv\n", "Processing file: datasets/COS_normal_error_code/214.csv\n", "Processing file: datasets/COS_normal_error_code/215.csv\n", "Processing file: datasets/COS_normal_error_code/216.csv\n", "Processing file: datasets/COS_normal_error_code/217.csv\n", "Processing file: datasets/COS_normal_error_code/218.csv\n", "Processing file: datasets/COS_normal_error_code/219.csv\n", "Processing file: datasets/COS_normal_error_code/220.csv\n", "Processing file: datasets/COS_normal_error_code/221.csv\n", "Processing file: datasets/COS_normal_error_code/222.csv\n", "Processing file: datasets/COS_normal_error_code/223.csv\n", "Processing file: datasets/COS_normal_error_code/224.csv\n", "Processing file: datasets/COS_normal_error_code/225.csv\n", "Processing file: datasets/COS_normal_error_code/226.csv\n", "Processing file: datasets/COS_normal_error_code/227.csv\n", "Processing file: datasets/COS_normal_error_code/228.csv\n", "Processing file: datasets/COS_normal_error_code/229.csv\n", "Processing file: datasets/COS_normal_error_code/230.csv\n", "Processing file: datasets/COS_normal_error_code/231.csv\n", "Processing file: datasets/COS_normal_error_code/232.csv\n", "Processing file: datasets/COS_normal_error_code/233.csv\n", "Processing file: datasets/COS_normal_error_code/234.csv\n", "Processing file: datasets/COS_normal_error_code/235.csv\n", "Processing file: datasets/COS_normal_error_code/236.csv\n", "Processing file: datasets/COS_normal_error_code/237.csv\n", "Processing file: datasets/COS_normal_error_code/238.csv\n", "Processing file: datasets/COS_normal_error_code/239.csv\n", "Processing file: datasets/COS_normal_error_code/240.csv\n", "Processing file: datasets/COS_normal_error_code/241.csv\n", "Processing file: datasets/COS_normal_error_code/242.csv\n", "Processing file: datasets/COS_normal_error_code/243.csv\n", "Processing file: datasets/COS_normal_error_code/244.csv\n", "Processing file: datasets/COS_normal_error_code/245.csv\n", "Processing file: datasets/COS_normal_error_code/246.csv\n", "Processing file: datasets/COS_normal_error_code/247.csv\n", "Processing file: datasets/COS_normal_error_code/248.csv\n", "Processing file: datasets/COS_normal_error_code/249.csv\n", "Processing file: datasets/COS_normal_error_code/250.csv\n", "Processing file: datasets/COS_normal_error_code/251.csv\n", "Processing file: datasets/COS_normal_error_code/252.csv\n", "Processing file: datasets/COS_normal_error_code/253.csv\n", "Processing file: datasets/COS_normal_error_code/254.csv\n", "Processing file: datasets/COS_normal_error_code/255.csv\n", "Processing file: datasets/COS_normal_error_code/256.csv\n", "Processing file: datasets/COS_normal_error_code/257.csv\n", "Processing file: datasets/COS_normal_error_code/258.csv\n", "Processing file: datasets/COS_normal_error_code/259.csv\n", "Processing file: datasets/COS_normal_error_code/260.csv\n", "Processing file: datasets/COS_normal_error_code/261.csv\n", "Processing file: datasets/COS_normal_error_code/262.csv\n", "Processing file: datasets/COS_normal_error_code/263.csv\n", "Processing file: datasets/COS_normal_error_code/264.csv\n", "Processing file: datasets/COS_normal_error_code/265.csv\n", "Processing file: datasets/COS_normal_error_code/266.csv\n", "Processing file: datasets/COS_normal_error_code/267.csv\n", "Processing file: datasets/COS_normal_error_code/268.csv\n", "Processing file: datasets/COS_normal_error_code/269.csv\n", "Processing file: datasets/COS_normal_error_code/270.csv\n", "Processing file: datasets/COS_normal_error_code/271.csv\n", "Processing file: datasets/COS_normal_error_code/272.csv\n", "Processing file: datasets/COS_normal_error_code/273.csv\n", "Processing file: datasets/COS_normal_error_code/274.csv\n", "Processing file: datasets/COS_normal_error_code/275.csv\n", "Processing file: datasets/COS_normal_error_code/276.csv\n", "Processing file: datasets/COS_normal_error_code/277.csv\n", "Processing file: datasets/COS_normal_error_code/278.csv\n", "Processing file: datasets/COS_normal_error_code/279.csv\n", "Processing file: datasets/COS_normal_error_code/280.csv\n", "Processing file: datasets/COS_normal_error_code/281.csv\n", "Processing file: datasets/COS_normal_error_code/282.csv\n", "Processing file: datasets/COS_normal_error_code/283.csv\n", "Processing file: datasets/COS_normal_error_code/284.csv\n", "Processing file: datasets/COS_normal_error_code/285.csv\n", "Processing file: datasets/COS_normal_error_code/286.csv\n", "Processing file: datasets/COS_normal_error_code/287.csv\n", "Processing file: datasets/COS_normal_error_code/288.csv\n", "Processing file: datasets/COS_normal_error_code/289.csv\n", "Processing file: datasets/COS_normal_error_code/290.csv\n", "Processing file: datasets/COS_normal_error_code/291.csv\n", "Processing file: datasets/COS_normal_error_code/292.csv\n", "Processing file: datasets/COS_normal_error_code/293.csv\n", "Processing file: datasets/COS_normal_error_code/294.csv\n", "Processing file: datasets/COS_normal_error_code/295.csv\n", "Processing file: datasets/COS_normal_error_code/296.csv\n", "Processing file: datasets/COS_normal_error_code/297.csv\n", "Processing file: datasets/COS_normal_error_code/298.csv\n", "Processing file: datasets/COS_normal_error_code/299.csv\n", "Processing file: datasets/COS_normal_error_code/300.csv\n", "Processing file: datasets/COS_normal_error_code/301.csv\n", "Processing file: datasets/COS_normal_error_code/302.csv\n", "Processing file: datasets/COS_normal_error_code/303.csv\n", "Processing file: datasets/COS_normal_error_code/304.csv\n", "Processing file: datasets/COS_normal_error_code/305.csv\n", "Processing file: datasets/COS_normal_error_code/306.csv\n", "Processing file: datasets/COS_normal_error_code/307.csv\n", "Processing file: datasets/COS_normal_error_code/308.csv\n", "Processing file: datasets/COS_normal_error_code/309.csv\n", "Processing file: datasets/COS_normal_error_code/310.csv\n", "Processing file: datasets/COS_normal_error_code/311.csv\n", "Processing file: datasets/COS_normal_error_code/312.csv\n"]}], "source": ["import os\n", "for i, file in enumerate(files):\n", "    print(f\"Processing file: {file}\")\n", "    cur = pd.read_csv(file)\n", "\n", "    cur = cur[cur['change_status']==1]\n", "\n", "    cur['now_prediction'] = now_preds_list[i]\n", "    cur['now_score'] = now_scores_list[i]\n", "\n", "    cur['timestamp'] = pd.to_datetime(cur['timestamp'])\n", "    cur.set_index('timestamp', inplace=True)\n", "    \n", "    plt.figure(figsize=(50, 8))\n", "    \n", "    # Plot the main value curve\n", "    cur['value'].plot(color='blue', label='Value')\n", "    \n", "    # Get value range for positioning\n", "    max_value = cur['value'].max()\n", "    min_value = cur['value'].min()\n", "    \n", "    # Plot is_anomaly as orange background regions\n", "    anomaly_mask = cur['is_anomaly'] == 1\n", "    if anomaly_mask.any():\n", "        # Create orange background for anomaly regions\n", "        plt.fill_between(cur.index, min_value, max_value, \n", "                        where=anomaly_mask, alpha=0.3, color='orange', \n", "                        label='Ground Truth Anomaly')\n", "    \n", "    # Plot now_prediction as red dots on the value curve\n", "    prediction_mask = cur['now_prediction'] == 1\n", "    if prediction_mask.any():\n", "        prediction_data = cur.loc[prediction_mask, 'value']\n", "        prediction_data.plot(style='ro', markersize=10, label='Prediction Anomaly', \n", "                    markeredgecolor='darkred', markeredgewidth=0.5) \n", "    \n", "    plt.legend()\n", "    plt.title(f'Anomaly Detection Comparison - {file}')\n", "    plt.xlabel('Timestamp')\n", "    plt.ylabel('Value')\n", "    if (raw_report['total_anomaly'] != 0 and raw_report['F1-score'] < 0.8) or (raw_report['total_fp'] > 2 and raw_report['F1-score'] < 0.8):\n", "        save_dir = f\"plots/{file}\"\n", "        os.makedirs(save_dir, exist_ok=True)\n", "        plt.savefig(os.path.join(save_dir, \"integration.png\"), dpi=300, bbox_inches='tight')\n", "        plt.close()\n", "\n", "        plt.figure(figsize=(50, 8))\n", "        cur['now_score'].plot(color='green')\n", "        plt.title(f'Anomaly Score - {file}')\n", "        plt.xlabel('Timestamp')\n", "        plt.ylabel('Score')\n", "        plt.savefig(os.path.join(save_dir, \"score.png\"), bbox_inches='tight')\n", "        plt.close()\n", "    else:\n", "        save_dir = f\"plots/{file}\"\n", "        os.makedirs(save_dir, exist_ok=True)\n", "        plt.savefig(os.path.join(save_dir, \"integration.png\"), bbox_inches='tight')\n", "        plt.close()\n", "\n", "        plt.figure(figsize=(50, 8))\n", "        cur['now_score'].plot(color='green')\n", "        plt.title(f'Anomaly Score - {file}')\n", "        plt.xlabel('Timestamp')\n", "        plt.ylabel('Score')\n", "        plt.savefig(os.path.join(save_dir, \"score.png\"), bbox_inches='tight')\n", "        plt.close()\n"]}, {"cell_type": "code", "execution_count": 7, "id": "e5ff2988", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["datasets/COS_normal_error_code/50.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/50.csv\n", "Raw report: {'F1-score': 0.7788161993769466, 'Precision': 0.9615384615384616, 'Recall': 0.6544502617801047, 'total_tp': 375, 'total_fp': 15, 'total_anomaly': 573}\n", "datasets/COS_normal_error_code/138.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/138.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 0, 'total_anomaly': 40}\n", "datasets/COS_normal_error_code/161.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/161.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 0, 'total_anomaly': 94}\n", "datasets/COS_normal_error_code/183.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/183.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 0, 'total_anomaly': 98}\n", "datasets/COS_normal_error_code/189.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/189.csv\n", "Raw report: {'F1-score': 0.7472527472527468, 'Precision': 0.9855072463768116, 'Recall': 0.6017699115044248, 'total_tp': 68, 'total_fp': 1, 'total_anomaly': 113}\n", "datasets/COS_normal_error_code/249.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/249.csv\n", "Raw report: {'F1-score': 0.6756756756756751, 'Precision': 0.7142857142857143, 'Recall': 0.6410256410256411, 'total_tp': 25, 'total_fp': 10, 'total_anomaly': 39}\n", "datasets/COS_normal_error_code/268.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/268.csv\n", "Raw report: {'F1-score': 0.571428571428571, 'Precision': 0.7272727272727272, 'Recall': 0.47058823529411764, 'total_tp': 8, 'total_fp': 3, 'total_anomaly': 17}\n", "datasets/COS_normal_error_code/294.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/294.csv\n", "Raw report: {'F1-score': 0.7058823529411757, 'Precision': 0.5454545454545454, 'Recall': 0.9999999999999999, 'total_tp': 6, 'total_fp': 5, 'total_anomaly': 6}\n", "datasets/COS_normal_error_code/309.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/309.csv\n", "Raw report: {'F1-score': 0.638297872340425, 'Precision': 0.46875, 'Recall': 0.9999999999999999, 'total_tp': 15, 'total_fp': 17, 'total_anomaly': 15}\n"]}], "source": ["for i, file in enumerate(files):\n", "\n", "    raw_report = calculate_micro_f1_score([now_preds_list[i]], [labels_list[i]], mode='raw')\n", "\n", "    if raw_report['total_anomaly'] != 0 and raw_report['F1-score'] < 0.8:\n", "        print(file)\n", "        print(f\"Low F1-score detected in file: {file}\")\n", "        print(f\"Raw report: {raw_report}\")"]}, {"cell_type": "code", "execution_count": 8, "id": "9b9eeccf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["datasets/COS_normal_error_code/19.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/19.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 4, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/23.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/23.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 4, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/24.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/24.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 8, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/27.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/27.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 10, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/32.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/32.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 6, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/34.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/34.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 4, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/35.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/35.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 3, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/42.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/42.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 6, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/44.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/44.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 3, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/47.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/47.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 25, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/48.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/48.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 3, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/49.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/49.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 3, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/50.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/50.csv\n", "Raw report: {'F1-score': 0.7788161993769466, 'Precision': 0.9615384615384616, 'Recall': 0.6544502617801047, 'total_tp': 375, 'total_fp': 15, 'total_anomaly': 573}\n", "datasets/COS_normal_error_code/58.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/58.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 17, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/60.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/60.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 3, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/62.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/62.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 10, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/63.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/63.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 4, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/64.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/64.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 16, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/65.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/65.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 8, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/68.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/68.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 16, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/72.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/72.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 5, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/74.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/74.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 3, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/79.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/79.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 4, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/82.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/82.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 16, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/89.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/89.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 4, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/94.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/94.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 7, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/97.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/97.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 15, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/98.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/98.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 4, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/101.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/101.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 24, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/102.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/102.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 5, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/104.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/104.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 3, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/111.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/111.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 6, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/114.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/114.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 6, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/116.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/116.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 10, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/126.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/126.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 17, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/128.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/128.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 14, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/137.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/137.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 10, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/145.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/145.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 5, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/152.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/152.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 3, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/155.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/155.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 7, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/164.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/164.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 5, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/169.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/169.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 6, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/171.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/171.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 9, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/173.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/173.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 12, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/177.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/177.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 13, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/180.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/180.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 8, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/182.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/182.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 7, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/184.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/184.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 24, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/185.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/185.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 13, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/197.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/197.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 4, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/201.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/201.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 8, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/204.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/204.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 14, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/205.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/205.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 3, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/207.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/207.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 8, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/215.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/215.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 7, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/220.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/220.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 3, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/222.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/222.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 5, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/228.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/228.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 6, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/229.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/229.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 5, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/232.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/232.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 10, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/233.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/233.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 13, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/234.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/234.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 5, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/235.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/235.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 7, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/239.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/239.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 4, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/241.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/241.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 18, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/242.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/242.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 5, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/245.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/245.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 5, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/249.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/249.csv\n", "Raw report: {'F1-score': 0.6756756756756751, 'Precision': 0.7142857142857143, 'Recall': 0.6410256410256411, 'total_tp': 25, 'total_fp': 10, 'total_anomaly': 39}\n", "datasets/COS_normal_error_code/251.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/251.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 16, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/253.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/253.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 4, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/256.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/256.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 14, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/259.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/259.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 4, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/268.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/268.csv\n", "Raw report: {'F1-score': 0.571428571428571, 'Precision': 0.7272727272727272, 'Recall': 0.47058823529411764, 'total_tp': 8, 'total_fp': 3, 'total_anomaly': 17}\n", "datasets/COS_normal_error_code/271.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/271.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 6, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/281.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/281.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 4, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/284.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/284.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 20, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/290.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/290.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 7, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/291.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/291.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 8, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/293.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/293.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 3, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/294.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/294.csv\n", "Raw report: {'F1-score': 0.7058823529411757, 'Precision': 0.5454545454545454, 'Recall': 0.9999999999999999, 'total_tp': 6, 'total_fp': 5, 'total_anomaly': 6}\n", "datasets/COS_normal_error_code/295.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/295.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 18, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/296.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/296.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 3, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/300.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/300.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 5, 'total_anomaly': 0}\n", "datasets/COS_normal_error_code/309.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/309.csv\n", "Raw report: {'F1-score': 0.638297872340425, 'Precision': 0.46875, 'Recall': 0.9999999999999999, 'total_tp': 15, 'total_fp': 17, 'total_anomaly': 15}\n", "datasets/COS_normal_error_code/310.csv\n", "Low F1-score detected in file: datasets/COS_normal_error_code/310.csv\n", "Raw report: {'F1-score': 0.0, 'Precision': 0.0, 'Recall': 0.0, 'total_tp': 0, 'total_fp': 4, 'total_anomaly': 0}\n"]}], "source": ["for i, file in enumerate(files):\n", "\n", "    raw_report = calculate_micro_f1_score([now_preds_list[i]], [labels_list[i]], mode='raw')\n", "\n", "    if raw_report['total_fp'] > 2 and raw_report['F1-score'] < 0.8:\n", "        print(file)\n", "        print(f\"Low F1-score detected in file: {file}\")\n", "        print(f\"Raw report: {raw_report}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.17"}}, "nbformat": 4, "nbformat_minor": 5}