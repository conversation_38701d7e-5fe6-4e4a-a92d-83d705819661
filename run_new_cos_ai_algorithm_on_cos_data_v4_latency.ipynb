from typing import Dict
import numpy as np
import pandas as pd
import sys
from joblib import Parallel, delayed

# Import cos_ai_service modules - use exact imports from the original code
from cos_ai_algorithm_interfaces import CosAIServiceAlgo, calculate_micro_f1_score, calculate_micro_f1_score_v0

from pathlib import Path
import pandas as pd
import matplotlib.pyplot as plt
data_dir = './datasets/COS_avg_latency/'
data_dir = Path(data_dir)

files = [f for f in data_dir.iterdir() if f.is_file()]
files.sort(key=lambda x: int(x.name.split('.')[0]) if x.name.split('.')[0].isdigit() else float('inf'))

def flag_calculation(file, params):
    cur = pd.read_csv(file)
    alg_instance = CosAIServiceAlgo(params=params)

    test_len = cur['change_status'].sum()
    alg_instance.test_phase_serialize(cur, test_len=test_len, is_filter=True, consecutive_threshold=2)

    cur['now_prediction'] = alg_instance.anomaly_flag()

    cur['now_score'] = alg_instance.anomaly_score()

    cur = cur[cur['change_status']==1]

    return cur['now_prediction'].values, cur['now_score'].values, cur['prior_prediction'].values, cur['is_anomaly'].values

params = {
    "param_list": {'param_dtw_low': 0.198, 'param_dtw_high': 0.4, 'param_percent': 100.0, 'time_window_focus': 3, 'time_window_dtw': 4320, 'param_dtw': 0.17}
}

result_list = Parallel(n_jobs=16, backend='multiprocessing')(delayed(flag_calculation)(file, params) for file in files)

now_preds_list = []
now_scores_list = []
prior_preds_list = []
labels_list = []
for result in result_list:
    now_preds_list.append(result[0])
    now_scores_list.append(result[1])
    prior_preds_list.append(result[2])
    labels_list.append(result[3])

raw_report = calculate_micro_f1_score(now_preds_list, labels_list, mode='raw')

raw_report

import os
for i, file in enumerate(files):
    print(f"Processing file: {file}")
    cur = pd.read_csv(file)

    cur = cur[cur['change_status']==1]

    cur['now_prediction'] = now_preds_list[i]
    cur['now_score'] = now_scores_list[i]

    cur['timestamp'] = pd.to_datetime(cur['timestamp'])
    cur.set_index('timestamp', inplace=True)
    
    plt.figure(figsize=(50, 8))
    
    # Plot the main value curve
    cur['value'].plot(color='blue', label='Value')
    
    # Get value range for positioning
    max_value = cur['value'].max()
    min_value = cur['value'].min()
    
    # Plot is_anomaly as orange background regions
    anomaly_mask = cur['is_anomaly'] == 1
    if anomaly_mask.any():
        # Create orange background for anomaly regions
        plt.fill_between(cur.index, min_value, max_value, 
                        where=anomaly_mask, alpha=0.3, color='orange', 
                        label='Ground Truth Anomaly')
    
    # Plot now_prediction as red dots on the value curve
    prediction_mask = cur['now_prediction'] == 1
    if prediction_mask.any():
        prediction_data = cur.loc[prediction_mask, 'value']
        prediction_data.plot(style='ro', markersize=10, label='Prediction Anomaly', 
                    markeredgecolor='darkred', markeredgewidth=0.5) 
    
    plt.legend()
    plt.title(f'Anomaly Detection Comparison - {file}')
    plt.xlabel('Timestamp')
    plt.ylabel('Value')
    if (raw_report['total_anomaly'] != 0 and raw_report['F1-score'] < 0.8) or (raw_report['total_fp'] > 2 and raw_report['F1-score'] < 0.8):
        save_dir = f"plots/{file}"
        os.makedirs(save_dir, exist_ok=True)
        plt.savefig(os.path.join(save_dir, "integration.png"), dpi=300, bbox_inches='tight')
        plt.close()

        plt.figure(figsize=(50, 8))
        cur['now_score'].plot(color='green')
        plt.title(f'Anomaly Score - {file}')
        plt.xlabel('Timestamp')
        plt.ylabel('Score')
        plt.savefig(os.path.join(save_dir, "score.png"), bbox_inches='tight')
        plt.close()
    else:
        save_dir = f"plots/{file}"
        os.makedirs(save_dir, exist_ok=True)
        plt.savefig(os.path.join(save_dir, "integration.png"), bbox_inches='tight')
        plt.close()

        plt.figure(figsize=(50, 8))
        cur['now_score'].plot(color='green')
        plt.title(f'Anomaly Score - {file}')
        plt.xlabel('Timestamp')
        plt.ylabel('Score')
        plt.savefig(os.path.join(save_dir, "score.png"), bbox_inches='tight')
        plt.close()

for i, file in enumerate(files):

    raw_report = calculate_micro_f1_score([now_preds_list[i]], [labels_list[i]], mode='raw')

    if raw_report['total_anomaly'] != 0 and raw_report['F1-score'] < 0.8:
        print(file)
        print(f"Low F1-score detected in file: {file}")
        print(f"Raw report: {raw_report}")



for i, file in enumerate(files):

    raw_report = calculate_micro_f1_score([now_preds_list[i]], [labels_list[i]], mode='raw')

    if raw_report['total_fp'] > 2 and raw_report['F1-score'] < 0.8:
        print(file)
        print(f"Low F1-score detected in file: {file}")
        print(f"Raw report: {raw_report}")